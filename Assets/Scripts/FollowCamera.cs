using UnityEngine;
using UnityEngine.UI;

public class FollowCamera : MonoBehaviour
{
    private Transform player;

    [Header("Environment Overlay Effects")]
    public Image underwaterOverlayImage; // Drag your UI Image here in the Inspector
    public Color underwaterTint = new Color(0.1f, 0.3f, 0.5f, 0.4f); // Ocean tint color (blue)
    public Color cloudTint = new Color(0.9f, 0.8f, 0.7f, 0.3f); // Cloud tint color (beige-y pinkish white) - adjustable in inspector
    public float tintTransitionSpeed = 2.0f; // Speed of the fade in/out
    public float underwaterTransitionRange = 1.0f; // How far above/below the boundary the transition happens
    public float cloudTransitionRange = 1.0f; // How far above/below the cloud boundary the transition happens

    private Color clearTint = new Color(0f, 0f, 0f, 0f); // Fully transparent

    [Header("Camera parameters")]
    public float mouseSensitivity = 2f;
    public float distanceFromPlayer = 3f;
    public float heightOffset = 1.5f;

    // Zoom parameters
    public float minZoom = 2f;
    public float maxZoom = 5f;
    public float zoomSpeed = 1f;

    // Add smoothing parameters
    public float cameraSmoothTime = 0.1f;
    private Vector3 currentVelocity = Vector3.zero;

    [Header("Dynamic FOV")]
    [Tooltip("Base FOV when player is stationary")]
    public float baseFOV = 60f;
    [Tooltip("Maximum FOV when player is at max speed")]
    public float maxFOV = 80f;
    [Tooltip("How smoothly FOV changes with speed")]
    public float fovSmoothTime = 0.2f;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    private float yaw = 0f;
    private float pitch = 0f;
    public float minPitch = -40f, maxPitch = 70f;
    public float minDistance = 0.5f;
    public float playerDriftFactor = 0.5f;

    private PlayerController pc;
    private float oceanBottomY;
    private float oceanTopY;
    private float cloudsBottomY;
    private float cloudsTopY;
    private float groundY;

    // Keep track of the last position to smooth transitions
    private Vector3 lastCameraPosition;

    // Dynamic FOV variables
    private Camera cameraComponent;
    private float currentFOV;
    private float fovVelocity = 0f;

    void Start()
    {
        Cursor.lockState = CursorLockMode.Locked;
        // Initialize the last position
        lastCameraPosition = transform.position;

        player = GameManager.Instance.player.transform;
        pc = player.GetComponent<PlayerController>();

        // Initialize camera component and FOV
        cameraComponent = GetComponent<Camera>();
        currentFOV = baseFOV;
        if (cameraComponent != null)
        {
            cameraComponent.fieldOfView = currentFOV;
        }

        // Initialize positions
        oceanBottomY = GameManager.Instance.oceanBottom.position.y;
        oceanTopY = GameManager.Instance.oceanBottom.position.y;
        cloudsBottomY = GameManager.Instance.cloudsBottom.position.y;
        cloudsTopY = GameManager.Instance.cloudsTop.position.y;
        groundY = GameManager.Instance.ground.position.y;
    }

    void LateUpdate()
    {
        // Process mouse input
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        yaw += mouseX;
        pitch -= mouseY;  // This makes pitch negative when looking up in your setup
        pitch = Mathf.Clamp(pitch, minPitch, maxPitch);

        // Process scroll wheel for zoom
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        if (scrollInput != 0)
        {
            distanceFromPlayer = Mathf.Lerp(distanceFromPlayer, distanceFromPlayer - scrollInput * zoomSpeed, 0.5f);
            distanceFromPlayer = Mathf.Clamp(distanceFromPlayer, minZoom, maxZoom);
        }

        // Handle camera positioning and rotation
        UpdateCameraPosition();

        // Handle dynamic FOV based on player speed
        UpdateDynamicFOV();

        // Handle underwater effect transition
        UpdateUnderwaterEffect();
    }

    void UpdateCameraPosition()
    {
        // Target position (player + height offset)
        Vector3 targetPosition = player.position + Vector3.up * heightOffset;

        // Standard rotation based on mouse input
        Quaternion rotation = Quaternion.Euler(pitch, yaw, 0);

        // Direction from target to camera
        Vector3 directionFromTarget = rotation * Vector3.back;

        // Standard desired position at full distance
        Vector3 desiredPosition = targetPosition + directionFromTarget * distanceFromPlayer;

        float groundHeight = groundY + 0.1f;

        Vector3 finalPosition = desiredPosition;
        float playerY = player.position.y; // Player's current vertical position

        // --- Vertical Clamping ---

        // Apply ground collision check first (updates finalPosition if needed)
        // This logic correctly handles the lowest boundary (ground).
        if (finalPosition.y < groundHeight)
        {
             float adjustedDistance = distanceFromPlayer;
             bool foundValidDistance = false;
             for (float testDist = distanceFromPlayer; testDist >= minDistance; testDist -= 0.1f)
             {
                 Vector3 testPos = player.position + Vector3.up * heightOffset + (Quaternion.Euler(pitch, yaw, 0) * Vector3.back) * testDist;
                 if (testPos.y >= groundHeight)
                 {
                     adjustedDistance = testDist;
                     foundValidDistance = true;
                     break;
                 }
             }
             if (foundValidDistance)
             {
                 finalPosition = player.position + Vector3.up * heightOffset + (Quaternion.Euler(pitch, yaw, 0) * Vector3.back) * adjustedDistance;
             }
             else
             {
                 finalPosition = player.position + Vector3.up + (Quaternion.Euler(pitch, yaw, 0) * Vector3.back) * minDistance;
                 finalPosition.y = groundHeight; // Ensure it's exactly at ground if no valid distance found
             }
        }
        // If not clamped by ground, apply other vertical boundary clamps based on player layer
        else
        {
            float cameraY = finalPosition.y; // Work with cameraY for clarity

            // Determine the camera's floor and ceiling based on the PLAYER'S layer

            float currentFloorY = groundHeight; // Default floor is ground
            float currentCeilingY = float.MaxValue; // Default ceiling is infinity

            // Player is in Clouds Layer or above (at or above clouds top)
            if (playerY >= cloudsBottomY)
            {
                currentFloorY = cloudsBottomY + 0.2f; // Clouds bottom is the floor while player is above it
                currentCeilingY = float.MaxValue; // No ceiling above in Space
            }
            // Player is in Ocean Layer (between ocean bottom and clouds bottom)
            else if (playerY >= oceanBottomY && playerY < cloudsBottomY)
            {
                currentFloorY = oceanBottomY + 0.2f; // Ocean bottom is the floor in Ocean layer
                currentCeilingY = cloudsBottomY - 1.2f; // Clouds bottom is the ceiling in Ocean layer
            }
            // Player is in UnderOcean Layer (between ground and ocean bottom)
            else if (playerY >= groundHeight && playerY < oceanBottomY)
            {
                 currentFloorY = groundHeight; // Ground is the floor in UnderOcean layer
                 currentCeilingY = oceanBottomY - 0.2f; // Ocean bottom is the ceiling in UnderOcean layer
            }
             // Player is Below UnderOcean (standing on the ground) - Ground collision handles this, no other vertical bounds needed here.


            // Clamp cameraY within the determined floor and ceiling for the player's layer
            cameraY = Mathf.Clamp(cameraY, currentFloorY, currentCeilingY);

            // Apply the clamped Y back to finalPosition
            finalPosition.y = cameraY;
        }


        // Apply smoothing to camera position to prevent jumps
        transform.position = Vector3.SmoothDamp(lastCameraPosition, finalPosition, ref currentVelocity, cameraSmoothTime);
        lastCameraPosition = transform.position;

        // Handle camera rotation based on pitch
        if (pitch < 0)
        {
            // Calculate drift amount - use absolute value since pitch is negative
            float pitchRatio = Mathf.Abs(pitch / minPitch);  // Using minPitch since that's the most negative value
            float driftAmount = pitchRatio * playerDriftFactor;

            // Calculate a modified look target with vertical offset
            Vector3 driftTarget = targetPosition + new Vector3(0, driftAmount * 2.0f, 0);

            // Look at the modified target instead of directly at player
            transform.LookAt(driftTarget);
        }
        else
        {
            // Not looking up, just look at the player
            transform.LookAt(targetPosition);
        }
    }

    void UpdateUnderwaterEffect()
    {
        if (underwaterOverlayImage == null) return;

        float cameraY = transform.position.y;
        Color targetColor = clearTint; // Default to clear

        // Determine which environment the camera is in and calculate appropriate tint
        if (cameraY >= cloudsBottomY)
        {
            // Camera is in cloud layer or above
            if (cameraY <= cloudsTopY)
            {
                // Camera is within cloud layer - apply cloud tint with transition
                float cloudLerpFactor = Mathf.InverseLerp(
                    cloudsBottomY - cloudTransitionRange / 2f, // Lower bound of cloud transition
                    cloudsBottomY + cloudTransitionRange / 2f, // Upper bound of cloud transition
                    cameraY
                );
                targetColor = Color.Lerp(clearTint, cloudTint, cloudLerpFactor);
            }
            else
            {
                // Camera is above clouds - check if we need transition out of clouds
                float cloudExitLerpFactor = Mathf.InverseLerp(
                    cloudsTopY + cloudTransitionRange / 2f, // Upper bound of cloud exit transition
                    cloudsTopY - cloudTransitionRange / 2f, // Lower bound of cloud exit transition
                    cameraY
                );
                targetColor = Color.Lerp(clearTint, cloudTint, cloudExitLerpFactor);
            }
        }
        else if (cameraY >= oceanBottomY)
        {
            // Camera is in ocean layer - apply underwater tint with transition
            float oceanLerpFactor = Mathf.InverseLerp(
                oceanBottomY - underwaterTransitionRange / 2f, // Lower bound of ocean transition
                oceanBottomY + underwaterTransitionRange / 2f, // Upper bound of ocean transition
                cameraY
            );
            targetColor = Color.Lerp(clearTint, underwaterTint, oceanLerpFactor);
        }
        // If camera is below ocean (underground), targetColor remains clearTint

        // Smoothly transition the overlay image color towards the target color over time
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        underwaterOverlayImage.color = Color.Lerp(underwaterOverlayImage.color, targetColor, clampedDeltaTime * tintTransitionSpeed);
    }

    void UpdateDynamicFOV()
    {
        if (cameraComponent == null || pc == null) return;

        // Get current player speed and max speed from PlayerController
        float currentPlayerSpeed = pc.GetCurrentPlayerSpeed();
        float maxPlayerSpeed = PlayerController.StaticMaxPlayerSpeed;

        // Calculate speed factor (0 to 1)
        float speedFactor = maxPlayerSpeed > 0f ? Mathf.Clamp01(currentPlayerSpeed / maxPlayerSpeed) : 0f;

        // Calculate target FOV based on speed factor
        float targetFOV = Mathf.Lerp(baseFOV, maxFOV, speedFactor);

        // Smoothly interpolate to target FOV
        currentFOV = Mathf.SmoothDamp(currentFOV, targetFOV, ref fovVelocity, fovSmoothTime);

        // Apply the FOV to the camera
        cameraComponent.fieldOfView = currentFOV;
    }
}