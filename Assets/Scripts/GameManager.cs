using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Centralized manager for global references and state.
/// </summary>
public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }

    [Header("References")]
    public PlayerController player;
    public Transform oceanBottom;
    public Transform cloudsBottom;
    public Transform cloudsTop;
    public Transform ground;
    public Camera mainCamera;
    public FollowCamera followCamera;

    [<PERSON><PERSON>("Cloud Layer Heights (Additive)")]
    [Tooltip("Height from cloudsBottom to start of color transition to lightning layer")]
    public float cloudStartLayerHeight = 5f;
    [Tooltip("Additional height for transition to lightning layer")]
    public float lightningTransitionHeight = 3f;
    [Tooltip("Additional height for lightning layer duration")]
    public float lightningLayerHeight = 6f;
    [Tooltip("Additional height for transition from lightning to clear layer")]
    public float clearTransitionHeight = 4f;

    [Header("Material Render Queue System")]
    [Tooltip("Materials that should have their render queue adjusted based on camera position")]
    public List<Material> dynamicRenderQueueMaterials = new List<Material>();

    [Tooltip("Render queue value when camera is below ocean (lower than water)")]
    public int belowOceanRenderQueue = 2999;

    [Tooltip("Render queue value when camera is above ocean (higher than water)")]
    public int aboveOceanRenderQueue = 3001;

    // Track current render queue state
    private bool isCameraBelowOcean = false;

    void Awake()
    {
        if (Instance != null && Instance != this) { Destroy(gameObject); return; }
        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    void Update()
    {
        // Update material render queues based on camera position
        UpdateMaterialRenderQueues();
    }

    /// <summary>
    /// Updates the render queue of all materials in the dynamicRenderQueueMaterials list
    /// based on the camera's position relative to the ocean.
    /// </summary>
    private void UpdateMaterialRenderQueues()
    {
        if (mainCamera == null || oceanBottom == null || dynamicRenderQueueMaterials.Count == 0)
            return;

        // Check if camera is below ocean
        bool cameraBelowOcean = mainCamera.transform.position.y < oceanBottom.position.y;

        // Only update render queue if state has changed
        if (cameraBelowOcean != isCameraBelowOcean)
        {
            isCameraBelowOcean = cameraBelowOcean;

            // Set the appropriate render queue
            int renderQueue = isCameraBelowOcean ? belowOceanRenderQueue : aboveOceanRenderQueue;

            // Apply to all materials in the list
            foreach (Material material in dynamicRenderQueueMaterials)
            {
                if (material != null)
                {
                    material.renderQueue = renderQueue;
                }
            }
        }
    }

    /// <summary>
    /// Adds a material to the dynamic render queue system.
    /// </summary>
    /// <param name="material">The material to add</param>
    public void AddDynamicRenderQueueMaterial(Material material)
    {
        if (material != null && !dynamicRenderQueueMaterials.Contains(material))
        {
            dynamicRenderQueueMaterials.Add(material);

            // Apply current render queue state immediately
            if (isCameraBelowOcean)
                material.renderQueue = belowOceanRenderQueue;
            else
                material.renderQueue = aboveOceanRenderQueue;
        }
    }

    /// <summary>
    /// Removes a material from the dynamic render queue system.
    /// </summary>
    /// <param name="material">The material to remove</param>
    public void RemoveDynamicRenderQueueMaterial(Material material)
    {
        if (material != null)
        {
            dynamicRenderQueueMaterials.Remove(material);
        }
    }

    /// <summary>
    /// Gets the calculated Y coordinates for cloud layer boundaries.
    /// </summary>
    public CloudLayerBoundaries GetCloudLayerBoundaries()
    {
        float cloudsBottomY = cloudsBottom != null ? cloudsBottom.position.y : 0f;

        return new CloudLayerBoundaries
        {
            cloudStartY = cloudsBottomY + cloudStartLayerHeight,
            lightningTransitionStartY = cloudsBottomY + cloudStartLayerHeight + lightningTransitionHeight,
            lightningLayerStartY = cloudsBottomY + cloudStartLayerHeight + lightningTransitionHeight + lightningLayerHeight,
            clearTransitionStartY = cloudsBottomY + cloudStartLayerHeight + lightningTransitionHeight + lightningLayerHeight + clearTransitionHeight
        };
    }
}

/// <summary>
/// Structure to hold calculated cloud layer Y boundaries.
/// </summary>
[System.Serializable]
public struct CloudLayerBoundaries
{
    public float cloudStartY;           // Start of initial cloud layer
    public float lightningTransitionStartY; // Start of transition to lightning layer
    public float lightningLayerStartY;     // Start of lightning layer
    public float clearTransitionStartY;    // Start of transition to clear layer
}
}
