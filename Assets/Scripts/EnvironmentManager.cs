using UnityEngine;

// Manages environment layers (ocean, clouds, space) and exposes gravity/maxFlyTime for each.
// Defines the distinct vertical layers of the game world.
public enum EnvironmentType { BelowOcean, Ocean, Clouds, AboveClouds }
 
public class EnvironmentManager : MonoBehaviour
{ 
    public static EnvironmentManager Instance { get; private set; }

    // Y-axis thresholds for each environmental layer.
    [Header("Environment Boundaries")]
    public float oceanBottomY = 20.0f;
    public float cloudsBottomY = 50.0f;
    public float cloudsTopY = 90.0f;

    [Header("Gravity Settings")]
    public float gravityBelowOcean = -8.0f;
    public float gravityInsideOcean = -4.0f;
    public float gravityInsideClouds = -2.0f;
    public float gravityAboveClouds = 0.0f;

    void Awake()
    // Standard Singleton pattern implementation.
    {
        if (Instance != null && Instance != this) { Destroy(gameObject); return; }
        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    // Determines the current environment type based on the given Y-coordinate.
    // y: The Y-coordinate to check.
    public EnvironmentType GetEnvironmentState(float y)
    {
        if (y < oceanBottomY) return EnvironmentType.BelowOcean;
        if (y < cloudsBottomY) return EnvironmentType.Ocean;
        if (y < cloudsTopY) return EnvironmentType.Clouds;
        return EnvironmentType.AboveClouds;
    }

    // Retrieves the gravity value for a specific environment type.
    // env: The environment type.
    public float GetGravity(EnvironmentType env)
    {
        return env switch
        {
            EnvironmentType.BelowOcean => gravityBelowOcean,
            EnvironmentType.Ocean => gravityInsideOcean,
            EnvironmentType.Clouds => gravityInsideClouds,
            EnvironmentType.AboveClouds => gravityAboveClouds,
            _ => gravityBelowOcean
        };
    }
}
