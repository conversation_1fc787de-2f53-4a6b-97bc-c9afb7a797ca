Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker1.log
-srvPort
63493
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 490169303 [EditorId] 490169303 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56963
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.001175 seconds.
- Loaded All Assemblies, in  0.333 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.314 seconds
Domain Reload Profiling: 648ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (123ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (315ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (112ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.696 seconds
Refreshing native plugins compatible for Editor in 11.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1402ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (182ms)
				TypeCache.ScanAssembly (162ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.04 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6814.
Memory consumption went from 166.9 MB to 163.6 MB.
Total: 10.841458 ms (FindLiveObjects: 0.820750 ms CreateObjectMapping: 0.263166 ms MarkObjects: 7.465500 ms  DeleteObjects: 2.291458 ms)

========================================================================
Received Import Request.
  Time since last request: 140408.197574 seconds.
  path: Assets/Materials/SnakeRiver_Invis.mat
  artifactKey: Guid(3a88fac13707b419f9d7ed8887661640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SnakeRiver_Invis.mat using Guid(3a88fac13707b419f9d7ed8887661640) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2be9dafcf28fd0dcced08794d11d601c') in 0.683329375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlueMaterial.mat
  artifactKey: Guid(7e0217b88181ee9498c17d872af6753e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlueMaterial.mat using Guid(7e0217b88181ee9498c17d872af6753e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f201125133f572277e8d3fa9e254c23') in 0.020909959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.813689 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/Greybox10xMaterial.mat
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/Greybox10xMaterial.mat using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f64675a3ce85b1452569dd8a4893d19c') in 0.01036725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.046921 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyMaterial.mat
  artifactKey: Guid(ae791e817e7146241b92f22d5d072488) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyMaterial.mat using Guid(ae791e817e7146241b92f22d5d072488) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '895874013e45786d326dd84129fcd887') in 0.00974625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 3.278414 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyClouds2D.mat
  artifactKey: Guid(c56646c00920b00419397d90bac5d646) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyClouds2D.mat using Guid(c56646c00920b00419397d90bac5d646) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf921e2791fc4268394b3f9dee37f815') in 7.434243334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1386.054727 seconds.
  path: Assets/Plugins/FMOD/images/SelectedAlt.png
  artifactKey: Guid(8ce9b717b1bc7564cbe35664f2f178a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SelectedAlt.png using Guid(8ce9b717b1bc7564cbe35664f2f178a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7b4e5b91eeb9b93c8ac415b3020663d') in 0.133414916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Assets/Plugins/FMOD/images/StudioIcon.png
  artifactKey: Guid(a4edfa5854cdec34b98b1c55f0562bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/StudioIcon.png using Guid(a4edfa5854cdec34b98b1c55f0562bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'efe7fc660ede82937f6d577d85611091') in 0.005808666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Plugins/FMOD/images/SnapshotIcon.png
  artifactKey: Guid(cf2bba5fb8be7e64ca39979f18eb372a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SnapshotIcon.png using Guid(cf2bba5fb8be7e64ca39979f18eb372a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5bc4ee6958006f7c402fa0ded1b21d3') in 0.005747583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Materials/Sprites/space.png
  artifactKey: Guid(e1eefd00ffcb149368f3956546ad127b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/space.png using Guid(e1eefd00ffcb149368f3956546ad127b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c0b81270c086426e6a27424737c1846') in 0.009344167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.894677 seconds.
  path: Assets/Plugins/FMOD/images/TransportPlayButtonOff.png
  artifactKey: Guid(29258b1336a580946bc144df00b74ac1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportPlayButtonOff.png using Guid(29258b1336a580946bc144df00b74ac1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7d98722762a4d9630aebce9f75f425d') in 0.008906583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.081981 seconds.
  path: Assets/Plugins/FMOD/images/TransportStopButtonOff.png
  artifactKey: Guid(cafa069c15865d543a07375373f0a18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportStopButtonOff.png using Guid(cafa069c15865d543a07375373f0a18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '019575269142ea52e3c38afbc1df5fd8') in 0.004259625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.251804 seconds.
  path: Assets/Materials/wattah/wata2.png
  artifactKey: Guid(e8eef33aef7c2449199b509b1d77ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/wata2.png using Guid(e8eef33aef7c2449199b509b1d77ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a8679e565d12619f60dbfc59c6784d1d') in 0.007140708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.257529 seconds.
  path: Assets/Materials/wattah/Water_002_NORM.jpg
  artifactKey: Guid(dbe8769b51d894057a6f16e2c12ad501) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/Water_002_NORM.jpg using Guid(dbe8769b51d894057a6f16e2c12ad501) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b0c0754d02a2c3d36bbe1c4aae340c4') in 0.006078125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.464246 seconds.
  path: Assets/Plugins/FMOD/images/Wrench.png
  artifactKey: Guid(507cd805ad331e54cb9e9cab5a9270b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Wrench.png using Guid(507cd805ad331e54cb9e9cab5a9270b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '827ea9bcfc24ec08e096d051f978800a') in 0.006709875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.844816 seconds.
  path: Assets/Plugins/FMOD/images/PreviewEmitter.png
  artifactKey: Guid(9519043db3741934fa01455c47683e8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/PreviewEmitter.png using Guid(9519043db3741934fa01455c47683e8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '027970d47b35a768e6cc7c60158e0d63') in 0.007869458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Materials/Sprites/ParticleSnakeTexture.png
  artifactKey: Guid(39ea767d6fddc4630a53279ecfb37c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/ParticleSnakeTexture.png using Guid(39ea767d6fddc4630a53279ecfb37c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68948a8a1ccd8540299758bfd9414e94') in 0.0285255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Materials/wattah/noiseTexture.png
  artifactKey: Guid(8b1fd47baf30c46d7856665c07a5c067) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/noiseTexture.png using Guid(8b1fd47baf30c46d7856665c07a5c067) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b9f0f675e9a92f0334a9ecfb4e32b7b') in 0.004587959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Plugins/FMOD/images/LevelMeter.png
  artifactKey: Guid(21e7a3d41a926364a8b9a6704ebe80d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LevelMeter.png using Guid(21e7a3d41a926364a8b9a6704ebe80d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b5db46116be9c5491edeb1eca7e25e9c') in 0.004391542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.003200 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Textures/Greybox.psd
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Textures/Greybox.psd using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '403560cf7422b3a0dd8bbe3387517904') in 0.006339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.434302 seconds.
  path: Assets/Plugins/FMOD/images/FolderIconClosed.png
  artifactKey: Guid(70efeb6d97126f843b30b8ed62d18a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FolderIconClosed.png using Guid(70efeb6d97126f843b30b8ed62d18a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94e8ae28683ee49bfd8ff889326aaa22') in 0.007010666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.017435 seconds.
  path: Assets/Plugins/FMOD/images/FMODLogoBlack.png
  artifactKey: Guid(36e46b3c334e47e41a0b4ff2f26905ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FMODLogoBlack.png using Guid(36e46b3c334e47e41a0b4ff2f26905ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b2b59042808f0470c2c3025168b3651') in 0.0041195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.051011 seconds.
  path: Assets/Plugins/FMOD/images/Delete.png
  artifactKey: Guid(196080340db65c44883dd3f599556fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Delete.png using Guid(196080340db65c44883dd3f599556fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83d47dcbf0d3298b21c17bc9769af2a0') in 0.003602458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026358 seconds.
  path: Assets/Plugins/FMOD/images/CopyIcon.png
  artifactKey: Guid(6e164dcb85fc8ad4b9ab2f1e883862d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/CopyIcon.png using Guid(6e164dcb85fc8ad4b9ab2f1e883862d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '989a3ba85596ce9c284c48d88659ff41') in 0.004179208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027681 seconds.
  path: Assets/Materials/Sprites/clouds.png
  artifactKey: Guid(4667168974324450aa73c87c33fbd361) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/clouds.png using Guid(4667168974324450aa73c87c33fbd361) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e6ae29ccb671686239bd8bbc11ec4a4') in 0.004899833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027012 seconds.
  path: Assets/Materials/Sprites/box_128_fade.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/box_128_fade.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '76c83a3693f0f394ac3a0c6a88c66b7f') in 0.003378208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.079517 seconds.
  path: Assets/Plugins/FMOD/images/Border.png
  artifactKey: Guid(40848578d1961334d820821bec6175a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Border.png using Guid(40848578d1961334d820821bec6175a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b87a4ea7dc4cc6a048e62e4c1646668') in 0.008217125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.142168 seconds.
  path: Assets/Plugins/FMOD/images/ArrowIcon.png
  artifactKey: Guid(01c0101f357b9da4ba78b8f58c290f86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/ArrowIcon.png using Guid(01c0101f357b9da4ba78b8f58c290f86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0845405b9e868243e8fe9d2135c69034') in 0.0080305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32c6d3000 may have been prematurely finalized
- Loaded All Assemblies, in  0.771 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.662 seconds
Domain Reload Profiling: 1436ms
	BeginReloadAssembly (306ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (406ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (449ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.2 MB). Loaded Objects now: 6906.
Memory consumption went from 170.2 MB to 166.0 MB.
Total: 7.653542 ms (FindLiveObjects: 0.513667 ms CreateObjectMapping: 0.190458 ms MarkObjects: 5.263667 ms  DeleteObjects: 1.685083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32c6d3000 may have been prematurely finalized
- Loaded All Assemblies, in  0.682 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1355ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (407ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 8.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.2 MB). Loaded Objects now: 6908.
Memory consumption went from 163.4 MB to 161.2 MB.
Total: 13.654667 ms (FindLiveObjects: 1.188208 ms CreateObjectMapping: 0.379417 ms MarkObjects: 9.517833 ms  DeleteObjects: 2.568417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30d287000 may have been prematurely finalized
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1288ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6910.
Memory consumption went from 163.4 MB to 160.9 MB.
Total: 11.475125 ms (FindLiveObjects: 0.719291 ms CreateObjectMapping: 0.281834 ms MarkObjects: 8.787333 ms  DeleteObjects: 1.686250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.9 MB). Loaded Objects now: 6911.
Memory consumption went from 153.5 MB to 150.6 MB.
Total: 26.800167 ms (FindLiveObjects: 0.451917 ms CreateObjectMapping: 0.242083 ms MarkObjects: 24.379750 ms  DeleteObjects: 1.726042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f53b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 5.06 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.719 seconds
Domain Reload Profiling: 1388ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (170ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (719ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (419ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6913.
Memory consumption went from 163.4 MB to 160.0 MB.
Total: 7.465875 ms (FindLiveObjects: 0.625292 ms CreateObjectMapping: 0.306708 ms MarkObjects: 4.726500 ms  DeleteObjects: 1.806875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f53b000 may have been prematurely finalized
- Loaded All Assemblies, in  6.420 seconds
Refreshing native plugins compatible for Editor in 127.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.901 seconds
Domain Reload Profiling: 7334ms
	BeginReloadAssembly (4036ms)
		ExecutionOrderSort (3ms)
		DisableScriptedObjects (514ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (1103ms)
	RebuildCommonClasses (136ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (91ms)
	LoadAllAssembliesAndSetupDomain (2155ms)
		LoadAssemblies (2916ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (508ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (326ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (902ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (455ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 6.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.3 MB). Loaded Objects now: 6915.
Memory consumption went from 163.3 MB to 161.0 MB.
Total: 51.732125 ms (FindLiveObjects: 1.081958 ms CreateObjectMapping: 0.674750 ms MarkObjects: 45.174667 ms  DeleteObjects: 4.799916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  2.082 seconds
Refreshing native plugins compatible for Editor in 11.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.797 seconds
Domain Reload Profiling: 2882ms
	BeginReloadAssembly (895ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (53ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (266ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (1103ms)
		LoadAssemblies (982ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (447ms)
			TypeCache.Refresh (93ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (323ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (797ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (578ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6917.
Memory consumption went from 163.4 MB to 160.4 MB.
Total: 9.904917 ms (FindLiveObjects: 0.746542 ms CreateObjectMapping: 0.397875 ms MarkObjects: 6.323125 ms  DeleteObjects: 2.436792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.517 seconds
Refreshing native plugins compatible for Editor in 3.12 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.712 seconds
Domain Reload Profiling: 2232ms
	BeginReloadAssembly (802ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (63ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (257ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (633ms)
		LoadAssemblies (750ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (551ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (399ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.4 MB). Loaded Objects now: 6919.
Memory consumption went from 163.4 MB to 161.0 MB.
Total: 11.335792 ms (FindLiveObjects: 0.753667 ms CreateObjectMapping: 0.459833 ms MarkObjects: 6.666833 ms  DeleteObjects: 3.454958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.170 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1826ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (119ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (710ms)
		LoadAssemblies (606ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (654ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (521ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (394ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6921.
Memory consumption went from 163.4 MB to 159.6 MB.
Total: 7.192875 ms (FindLiveObjects: 0.476833 ms CreateObjectMapping: 0.274375 ms MarkObjects: 4.768459 ms  DeleteObjects: 1.672417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  2.043 seconds
Refreshing native plugins compatible for Editor in 3.52 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.044 seconds
Domain Reload Profiling: 3092ms
	BeginReloadAssembly (950ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (92ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (169ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (995ms)
		LoadAssemblies (985ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (411ms)
			TypeCache.Refresh (67ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (301ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1045ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (629ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 3.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.9 MB). Loaded Objects now: 6923.
Memory consumption went from 163.4 MB to 158.5 MB.
Total: 59.854667 ms (FindLiveObjects: 12.792125 ms CreateObjectMapping: 0.569583 ms MarkObjects: 37.461917 ms  DeleteObjects: 9.029792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.066 seconds
Refreshing native plugins compatible for Editor in 2.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.700 seconds
Domain Reload Profiling: 1770ms
	BeginReloadAssembly (382ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (189ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (613ms)
		LoadAssemblies (472ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (701ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (551ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6925.
Memory consumption went from 163.3 MB to 159.4 MB.
Total: 21.016000 ms (FindLiveObjects: 0.588750 ms CreateObjectMapping: 0.440791 ms MarkObjects: 17.207000 ms  DeleteObjects: 2.778833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.330 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.78 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.640 seconds
Domain Reload Profiling: 1973ms
	BeginReloadAssembly (651ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (191ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (619ms)
		LoadAssemblies (683ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (640ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6927.
Memory consumption went from 163.4 MB to 160.6 MB.
Total: 7.943125 ms (FindLiveObjects: 0.411667 ms CreateObjectMapping: 0.229000 ms MarkObjects: 5.823500 ms  DeleteObjects: 1.478542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.867 seconds
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.728 seconds
Domain Reload Profiling: 1600ms
	BeginReloadAssembly (288ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (130ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (728ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (565ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (413ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.4 MB). Loaded Objects now: 6929.
Memory consumption went from 163.4 MB to 161.0 MB.
Total: 11.303167 ms (FindLiveObjects: 1.329542 ms CreateObjectMapping: 0.366416 ms MarkObjects: 5.996084 ms  DeleteObjects: 3.609958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.718 seconds
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.654 seconds
Domain Reload Profiling: 1376ms
	BeginReloadAssembly (258ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (406ms)
		LoadAssemblies (263ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (654ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (514ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6931.
Memory consumption went from 163.4 MB to 160.1 MB.
Total: 11.439958 ms (FindLiveObjects: 0.557250 ms CreateObjectMapping: 0.271250 ms MarkObjects: 8.555583 ms  DeleteObjects: 2.055208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.781 seconds
Refreshing native plugins compatible for Editor in 2.67 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.725 seconds
Domain Reload Profiling: 2509ms
	BeginReloadAssembly (681ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (375ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (996ms)
		LoadAssemblies (901ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (725ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6933.
Memory consumption went from 163.4 MB to 160.6 MB.
Total: 7.742875 ms (FindLiveObjects: 0.578542 ms CreateObjectMapping: 0.322625 ms MarkObjects: 5.221333 ms  DeleteObjects: 1.619833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.942 seconds
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.754 seconds
Domain Reload Profiling: 1700ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (140ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (424ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (755ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (612ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (465ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 5.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.9 MB). Loaded Objects now: 6935.
Memory consumption went from 163.3 MB to 160.5 MB.
Total: 34.523458 ms (FindLiveObjects: 5.428166 ms CreateObjectMapping: 1.523625 ms MarkObjects: 22.576250 ms  DeleteObjects: 4.994750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.761 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1428ms
	BeginReloadAssembly (274ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (421ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (399ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6937.
Memory consumption went from 163.4 MB to 160.1 MB.
Total: 9.050333 ms (FindLiveObjects: 0.543875 ms CreateObjectMapping: 0.427292 ms MarkObjects: 6.196250 ms  DeleteObjects: 1.882542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.210 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1866ms
	BeginReloadAssembly (498ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (151ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (641ms)
		LoadAssemblies (625ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (501ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (369ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.6 MB). Loaded Objects now: 6939.
Memory consumption went from 163.4 MB to 159.9 MB.
Total: 11.904333 ms (FindLiveObjects: 0.722042 ms CreateObjectMapping: 0.735625 ms MarkObjects: 8.405833 ms  DeleteObjects: 2.040125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.325 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.698 seconds
Domain Reload Profiling: 2026ms
	BeginReloadAssembly (554ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (94ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (617ms)
		LoadAssemblies (530ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (297ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (242ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (543ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6941.
Memory consumption went from 163.4 MB to 160.0 MB.
Total: 7.700375 ms (FindLiveObjects: 0.472792 ms CreateObjectMapping: 0.214958 ms MarkObjects: 5.137000 ms  DeleteObjects: 1.875041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.664 seconds
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.662 seconds
Domain Reload Profiling: 1329ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (662ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6943.
Memory consumption went from 163.4 MB to 160.6 MB.
Total: 10.107959 ms (FindLiveObjects: 0.778833 ms CreateObjectMapping: 1.689500 ms MarkObjects: 5.605083 ms  DeleteObjects: 2.033542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.859 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.636 seconds
Domain Reload Profiling: 1499ms
	BeginReloadAssembly (247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (524ms)
		LoadAssemblies (392ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (470ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (343ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.6 MB). Loaded Objects now: 6945.
Memory consumption went from 163.4 MB to 158.7 MB.
Total: 10.051917 ms (FindLiveObjects: 1.156375 ms CreateObjectMapping: 0.249125 ms MarkObjects: 5.942125 ms  DeleteObjects: 2.703792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.638 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.588 seconds
Domain Reload Profiling: 1229ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (147ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (588ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (454ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6947.
Memory consumption went from 163.4 MB to 158.9 MB.
Total: 13.788084 ms (FindLiveObjects: 0.486833 ms CreateObjectMapping: 0.326250 ms MarkObjects: 10.480833 ms  DeleteObjects: 2.493334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.882 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1653ms
	BeginReloadAssembly (269ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (112ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (552ms)
		LoadAssemblies (406ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.3 MB). Loaded Objects now: 6949.
Memory consumption went from 163.4 MB to 159.1 MB.
Total: 15.308000 ms (FindLiveObjects: 0.526792 ms CreateObjectMapping: 0.385708 ms MarkObjects: 11.496750 ms  DeleteObjects: 2.897459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.715 seconds
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.617 seconds
Domain Reload Profiling: 1335ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (206ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (214ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (342ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (6.1 MB). Loaded Objects now: 6951.
Memory consumption went from 163.4 MB to 157.4 MB.
Total: 9.362917 ms (FindLiveObjects: 0.487334 ms CreateObjectMapping: 0.191834 ms MarkObjects: 4.937583 ms  DeleteObjects: 3.743834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  1.114 seconds
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.669 seconds
Domain Reload Profiling: 1787ms
	BeginReloadAssembly (433ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (591ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (238ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (6.1 MB). Loaded Objects now: 6953.
Memory consumption went from 163.4 MB to 157.3 MB.
Total: 21.574417 ms (FindLiveObjects: 5.763000 ms CreateObjectMapping: 0.919958 ms MarkObjects: 10.564459 ms  DeleteObjects: 4.326333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.791 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.640 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (307ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (641ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6955.
Memory consumption went from 163.4 MB to 160.1 MB.
Total: 21.598792 ms (FindLiveObjects: 0.490916 ms CreateObjectMapping: 0.405292 ms MarkObjects: 17.653542 ms  DeleteObjects: 3.048583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.778 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.48 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.840 seconds
Domain Reload Profiling: 1622ms
	BeginReloadAssembly (234ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (466ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (841ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6957.
Memory consumption went from 163.4 MB to 160.2 MB.
Total: 18.392417 ms (FindLiveObjects: 0.489167 ms CreateObjectMapping: 0.658000 ms MarkObjects: 13.692875 ms  DeleteObjects: 3.551875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.941 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.728 seconds
Domain Reload Profiling: 1674ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (618ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (333ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (730ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.9 MB). Loaded Objects now: 6959.
Memory consumption went from 163.4 MB to 158.5 MB.
Total: 18.097667 ms (FindLiveObjects: 0.486625 ms CreateObjectMapping: 0.376375 ms MarkObjects: 13.832125 ms  DeleteObjects: 3.401708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.772 seconds
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.658 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (258ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (659ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (508ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 4.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6961.
Memory consumption went from 163.4 MB to 160.1 MB.
Total: 18.517875 ms (FindLiveObjects: 0.850958 ms CreateObjectMapping: 0.447625 ms MarkObjects: 14.505750 ms  DeleteObjects: 2.712917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.714 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.641 seconds
Domain Reload Profiling: 1359ms
	BeginReloadAssembly (227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (431ms)
		LoadAssemblies (240ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (642ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (368ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.7 MB). Loaded Objects now: 6963.
Memory consumption went from 163.4 MB to 158.8 MB.
Total: 8.874458 ms (FindLiveObjects: 0.573250 ms CreateObjectMapping: 0.285417 ms MarkObjects: 5.637458 ms  DeleteObjects: 2.378042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.763 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.68 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1404ms
	BeginReloadAssembly (227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (475ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 6965.
Memory consumption went from 163.4 MB to 159.6 MB.
Total: 18.318791 ms (FindLiveObjects: 1.053250 ms CreateObjectMapping: 0.550917 ms MarkObjects: 13.202167 ms  DeleteObjects: 3.511708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.745 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1413ms
	BeginReloadAssembly (250ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (436ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (664ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (514ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (377ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 4.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6967.
Memory consumption went from 163.5 MB to 160.3 MB.
Total: 19.574083 ms (FindLiveObjects: 1.284208 ms CreateObjectMapping: 0.903917 ms MarkObjects: 15.061666 ms  DeleteObjects: 2.323500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.819 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1459ms
	BeginReloadAssembly (238ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (228ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.4 MB). Loaded Objects now: 6969.
Memory consumption went from 163.5 MB to 158.1 MB.
Total: 10.584125 ms (FindLiveObjects: 0.611292 ms CreateObjectMapping: 0.348125 ms MarkObjects: 6.996458 ms  DeleteObjects: 2.625334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.703 seconds
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.625 seconds
Domain Reload Profiling: 1331ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (430ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (483ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.7 MB). Loaded Objects now: 6971.
Memory consumption went from 163.5 MB to 160.7 MB.
Total: 27.644542 ms (FindLiveObjects: 2.676417 ms CreateObjectMapping: 0.800625 ms MarkObjects: 21.218459 ms  DeleteObjects: 2.947458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.893 seconds
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.662 seconds
Domain Reload Profiling: 1559ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (609ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (380ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (336ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (372ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.1 MB). Loaded Objects now: 6973.
Memory consumption went from 163.5 MB to 158.4 MB.
Total: 8.662792 ms (FindLiveObjects: 0.502625 ms CreateObjectMapping: 0.278208 ms MarkObjects: 5.318959 ms  DeleteObjects: 2.562292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.698 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.628 seconds
Domain Reload Profiling: 1330ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (436ms)
		LoadAssemblies (246ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (628ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6975.
Memory consumption went from 163.4 MB to 160.2 MB.
Total: 10.061083 ms (FindLiveObjects: 0.737000 ms CreateObjectMapping: 0.353167 ms MarkObjects: 6.867875 ms  DeleteObjects: 2.102458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.867 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1536ms
	BeginReloadAssembly (274ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (527ms)
		LoadAssemblies (368ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (187ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.1 MB). Loaded Objects now: 6977.
Memory consumption went from 163.5 MB to 158.3 MB.
Total: 9.986792 ms (FindLiveObjects: 0.477250 ms CreateObjectMapping: 0.370000 ms MarkObjects: 5.639333 ms  DeleteObjects: 3.499792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.699 seconds
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.82 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.644 seconds
Domain Reload Profiling: 1346ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (398ms)
		LoadAssemblies (272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (644ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 5.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.1 MB). Loaded Objects now: 6979.
Memory consumption went from 163.5 MB to 159.4 MB.
Total: 9.704458 ms (FindLiveObjects: 1.322917 ms CreateObjectMapping: 0.341625 ms MarkObjects: 5.422250 ms  DeleteObjects: 2.617166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.648 seconds
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1271ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (246ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (619ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (342ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.6 MB). Loaded Objects now: 6981.
Memory consumption went from 163.5 MB to 158.8 MB.
Total: 12.420584 ms (FindLiveObjects: 1.749291 ms CreateObjectMapping: 0.450042 ms MarkObjects: 6.422333 ms  DeleteObjects: 3.797792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.720 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.624 seconds
Domain Reload Profiling: 1348ms
	BeginReloadAssembly (235ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (121ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (259ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (487ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (356ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6983.
Memory consumption went from 163.5 MB to 160.3 MB.
Total: 17.354958 ms (FindLiveObjects: 0.812750 ms CreateObjectMapping: 0.353250 ms MarkObjects: 14.285292 ms  DeleteObjects: 1.902083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.754 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.41 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1391ms
	BeginReloadAssembly (241ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (449ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6985.
Memory consumption went from 163.4 MB to 160.1 MB.
Total: 10.500209 ms (FindLiveObjects: 0.744792 ms CreateObjectMapping: 0.334083 ms MarkObjects: 6.931167 ms  DeleteObjects: 2.489459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.711 seconds
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.628 seconds
Domain Reload Profiling: 1343ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (436ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (629ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6987.
Memory consumption went from 163.5 MB to 160.3 MB.
Total: 8.889500 ms (FindLiveObjects: 0.858334 ms CreateObjectMapping: 0.353875 ms MarkObjects: 5.536667 ms  DeleteObjects: 2.139458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.699 seconds
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1368ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (404ms)
		LoadAssemblies (252ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (667ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6989.
Memory consumption went from 163.5 MB to 159.0 MB.
Total: 21.770542 ms (FindLiveObjects: 1.437125 ms CreateObjectMapping: 0.447417 ms MarkObjects: 17.700083 ms  DeleteObjects: 2.185208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.746 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.622 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (199ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (479ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6991.
Memory consumption went from 163.5 MB to 158.9 MB.
Total: 8.678208 ms (FindLiveObjects: 0.606958 ms CreateObjectMapping: 0.314875 ms MarkObjects: 5.368125 ms  DeleteObjects: 2.387416 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.705 seconds
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.629 seconds
Domain Reload Profiling: 1337ms
	BeginReloadAssembly (236ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (412ms)
		LoadAssemblies (257ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6993.
Memory consumption went from 163.5 MB to 159.6 MB.
Total: 13.939834 ms (FindLiveObjects: 1.007208 ms CreateObjectMapping: 0.453542 ms MarkObjects: 9.620083 ms  DeleteObjects: 2.857833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.736 seconds
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.622 seconds
Domain Reload Profiling: 1362ms
	BeginReloadAssembly (240ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (439ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6995.
Memory consumption went from 163.4 MB to 160.3 MB.
Total: 12.527833 ms (FindLiveObjects: 0.573917 ms CreateObjectMapping: 0.308917 ms MarkObjects: 9.040083 ms  DeleteObjects: 2.603833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.756 seconds
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.639 seconds
Domain Reload Profiling: 1403ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (476ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (639ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (492ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6997.
Memory consumption went from 163.5 MB to 160.5 MB.
Total: 13.361625 ms (FindLiveObjects: 0.689667 ms CreateObjectMapping: 0.565000 ms MarkObjects: 8.876875 ms  DeleteObjects: 3.225542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.594 seconds
Domain Reload Profiling: 1265ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (390ms)
		LoadAssemblies (240ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (152ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (331ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6999.
Memory consumption went from 163.5 MB to 159.7 MB.
Total: 12.489750 ms (FindLiveObjects: 0.466125 ms CreateObjectMapping: 0.241666 ms MarkObjects: 9.109292 ms  DeleteObjects: 2.671750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.927 seconds
Refreshing native plugins compatible for Editor in 9.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.25 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.751 seconds
Domain Reload Profiling: 1682ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (584ms)
		LoadAssemblies (427ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (210ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (752ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (6.0 MB). Loaded Objects now: 7001.
Memory consumption went from 163.5 MB to 157.5 MB.
Total: 10.608042 ms (FindLiveObjects: 0.466375 ms CreateObjectMapping: 0.435208 ms MarkObjects: 6.153125 ms  DeleteObjects: 3.552834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16dbff000 may have been prematurely finalized
- Loaded All Assemblies, in  0.975 seconds
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 1998ms
	BeginReloadAssembly (319ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (407ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1019ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (715ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (549ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 7003.
Memory consumption went from 163.5 MB to 160.0 MB.
Total: 16.386542 ms (FindLiveObjects: 0.489584 ms CreateObjectMapping: 0.402833 ms MarkObjects: 13.105667 ms  DeleteObjects: 2.387708 ms)

Prepare: number of updated asset objects reloaded= 0
