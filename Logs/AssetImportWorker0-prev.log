Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
63493
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1832323524 [EditorId] 1832323524 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56961
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.003456 seconds.
- Loaded All Assemblies, in  0.333 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.315 seconds
Domain Reload Profiling: 649ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (123ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (47ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (108ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.695 seconds
Refreshing native plugins compatible for Editor in 11.74 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 1401ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (521ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (183ms)
				TypeCache.ScanAssembly (160ms)
			BuildScriptInfoCaches (44ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.04 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6814.
Memory consumption went from 167.1 MB to 163.6 MB.
Total: 9.958541 ms (FindLiveObjects: 1.003791 ms CreateObjectMapping: 0.258584 ms MarkObjects: 6.832416 ms  DeleteObjects: 1.860500 ms)

========================================================================
Received Import Request.
  Time since last request: 140408.196162 seconds.
  path: Assets/Tut/Clouds.mat
  artifactKey: Guid(e5ea6bb4f9e3d45d49f11cccedb533ac) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tut/Clouds.mat using Guid(e5ea6bb4f9e3d45d49f11cccedb533ac) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27db7b17921fe6d4b8065f79e249ca7e') in 0.697728458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlackMaterial.mat
  artifactKey: Guid(21aa06fceebc0a54fb182c7587193631) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlackMaterial.mat using Guid(21aa06fceebc0a54fb182c7587193631) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a6365768e5a206f6fa48e7acf11a543d') in 0.0674185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.770678 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreenMaterial.mat
  artifactKey: Guid(cf945f6d7077aff4ca37b5c58f80c2e2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreenMaterial.mat using Guid(cf945f6d7077aff4ca37b5c58f80c2e2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c1f11e1d72ecda86bfb8f21abce70a8') in 0.011452333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.046253 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyboxMaterial.mat
  artifactKey: Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyboxMaterial.mat using Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea87471eee73ef422c1b25a73e0a8869') in 0.009579208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.203398 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/RedMaterial.mat
  artifactKey: Guid(4f41629ff156d6045ba43af08e275039) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/RedMaterial.mat using Guid(4f41629ff156d6045ba43af08e275039) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6c6e8cb2d2436b6cd416892e60c60c5') in 0.023382083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.030447 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudIntersectionFade.mat
  artifactKey: Guid(60381268557768741b0498ef9caa18d1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudIntersectionFade.mat using Guid(60381268557768741b0498ef9caa18d1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9111226587fed950ccfcb2b24c7a049') in 0.010404792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.021300 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat
  artifactKey: Guid(8dd590b390be4264a87b624e0980e669) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat using Guid(8dd590b390be4264a87b624e0980e669) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45b0319b25369403df84b5c6ce3c9a29') in 0.017357209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000118 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsBig.mat
  artifactKey: Guid(4ff3d050c5199cc49b1ebd390ea0be62) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsBig.mat using Guid(4ff3d050c5199cc49b1ebd390ea0be62) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '586ac1ff7205492d953c40779f383eae') in 0.009010625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 4.240213 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsMid.mat
  artifactKey: Guid(c99f2b6a7d5063f4c8bf7fc0304a0545) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsMid.mat using Guid(c99f2b6a7d5063f4c8bf7fc0304a0545) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '948b25ff6306fb54279ae1bb0d1d74b7') in 0.011791875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/_Test/SkyCloudsPlane/SkyCloudsPlane.mat
  artifactKey: Guid(d49e9a268b12eeb48b392db14a6fb47b) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Test/SkyCloudsPlane/SkyCloudsPlane.mat using Guid(d49e9a268b12eeb48b392db14a6fb47b) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f40902ac3ff40e5c577bda7903fb183a') in 0.096011834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.4.mat
  artifactKey: Guid(ea7e4fbd8c8f6b045b703a668591c329) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.4.mat using Guid(ea7e4fbd8c8f6b045b703a668591c329) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e9c21d8dcb3a4f577cd985ad85405f3') in 0.008959584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_2.mat
  artifactKey: Guid(7b9d195a8c7b72d4cbcb1ce6b6107d8a) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_2.mat using Guid(7b9d195a8c7b72d4cbcb1ce6b6107d8a) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1be2871224d277085f22b1a4e4c8b33') in 0.007466917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_ControlledWind.mat
  artifactKey: Guid(32b06ee158459e3449c919090eb79363) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_ControlledWind.mat using Guid(32b06ee158459e3449c919090eb79363) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ace94f2d1483b565d7f4248e009ed1ae') in 0.0075395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.8.mat
  artifactKey: Guid(0f22d337e60da8149bb0ffc64e0c7dde) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.8.mat using Guid(0f22d337e60da8149bb0ffc64e0c7dde) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94337e801268cdc7af29f6263c5d26a6') in 0.010550458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Flat.mat
  artifactKey: Guid(70771b08ad6ef614dafbb010f1502713) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Flat.mat using Guid(70771b08ad6ef614dafbb010f1502713) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db041f1a8eb3fe71c79d2baecd5f9cf5') in 0.009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_NoiseColor.mat
  artifactKey: Guid(2889243cbb289b24c8f6ccbd585555f6) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_NoiseColor.mat using Guid(2889243cbb289b24c8f6ccbd585555f6) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7bb6cb85e1bac003261655b58b851c03') in 0.008254417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Colorful.mat
  artifactKey: Guid(6b5502d376d3f274abc6927d5ab28d4d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Colorful.mat using Guid(6b5502d376d3f274abc6927d5ab28d4d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3670ab35f5eb9102776a24b9bcbb44f5') in 0.009790458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_0.5.mat
  artifactKey: Guid(129ad2f4fcb647649ad7876d8f56f497) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_0.5.mat using Guid(129ad2f4fcb647649ad7876d8f56f497) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f56dc0771616a276bd3cc05e1fc5d3e2') in 0.008086541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsSmall.mat
  artifactKey: Guid(5453f3c4cad0b444ab476747ba6a82b2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsSmall.mat using Guid(5453f3c4cad0b444ab476747ba6a82b2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84cb92e952454a877389c1565003ed79') in 0.008332666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall.mat
  artifactKey: Guid(87047a5db84f44d479dbb94da4c1d217) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall.mat using Guid(87047a5db84f44d479dbb94da4c1d217) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3c760dee1c22160f4d4595cbb907763c') in 0.008625916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default.mat
  artifactKey: Guid(fcddd1cc1df5dfc47bc64c8211c3edae) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default.mat using Guid(fcddd1cc1df5dfc47bc64c8211c3edae) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5f1cedf8a282dbe4194ed80ffcdc0dd') in 0.008205458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_1.2.mat
  artifactKey: Guid(a55b2ff73341ebf4fa9e17b9af723a7e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_1.2.mat using Guid(a55b2ff73341ebf4fa9e17b9af723a7e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70b79ced662554cb9536720d9d524847') in 0.008151083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.121964 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_2.mat
  artifactKey: Guid(df8228e2063f02748ab26a17d1a1ed42) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_2.mat using Guid(df8228e2063f02748ab26a17d1a1ed42) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2734070324a144a83734374b7c181d13') in 0.012682125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_4.mat
  artifactKey: Guid(515eacba2a3d8324ab0c03e6ff144fb6) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_4.mat using Guid(515eacba2a3d8324ab0c03e6ff144fb6) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f464607057fa43be6d0cb4341f3d356c') in 0.00802875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_WindyGrey.mat
  artifactKey: Guid(b5732e4e24da22a46a5859f65c4915e9) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_WindyGrey.mat using Guid(b5732e4e24da22a46a5859f65c4915e9) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd42009af25c605ee78529acbe8ee1def') in 0.007677625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_SemiFlat.mat
  artifactKey: Guid(f3ca28c89f08c5241b7bdef619817aa8) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_SemiFlat.mat using Guid(f3ca28c89f08c5241b7bdef619817aa8) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a5e3c1f86c79b499164fb996d961c02') in 0.009429666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.002518 seconds.
  path: Assets/Materials/SnakeRiver_Tail.mat
  artifactKey: Guid(c6a3bbfd055b743a8acd36820bb87725) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SnakeRiver_Tail.mat using Guid(c6a3bbfd055b743a8acd36820bb87725) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc048a629b2ec4a009017764c08085c7') in 0.015630291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.362632 seconds.
  path: Assets/Materials/SoulCreatureGiant.mat
  artifactKey: Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SoulCreatureGiant.mat using Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ccf2a759dc887c74c74141d87ad123c') in 0.01136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1351.060607 seconds.
  path: Assets/Materials/Lightning.mat
  artifactKey: Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Lightning.mat using Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32a73552a37aa258b80d6ab4d60422a5') in 0.082924583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 36.791107 seconds.
  path: Assets/Plugins/FMOD/images/SearchIconBlack.png
  artifactKey: Guid(99471facfde9fb84dbe4a81ad570ebce) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SearchIconBlack.png using Guid(99471facfde9fb84dbe4a81ad570ebce) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e2fc879457dd181489e170bf807cc70') in 0.121216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/Plugins/FMOD/images/TickGreen.png
  artifactKey: Guid(27533226416c4f549b9bec9c024100f7) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TickGreen.png using Guid(27533226416c4f549b9bec9c024100f7) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16d5de7a6a3ecead0e74fc11618a92d1') in 0.0043975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Materials/Sprites/SoulCreatureGiant.png
  artifactKey: Guid(650c90e0bcc404e52ac828df436d0143) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/SoulCreatureGiant.png using Guid(650c90e0bcc404e52ac828df436d0143) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a1bcce7c54ad07ff2be4344ab9560efa') in 0.028019708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Materials/Sprites/splash.png
  artifactKey: Guid(70e249635be5348d88a4d93785761515) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/splash.png using Guid(70e249635be5348d88a4d93785761515) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b37e819d245e5911ab133ae9f164218') in 0.010065291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.871592 seconds.
  path: Assets/Plugins/FMOD/images/TransportOpen.png
  artifactKey: Guid(b0fb832e401d1514a9611735d8d340b1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportOpen.png using Guid(b0fb832e401d1514a9611735d8d340b1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f222eb2c11323fdbd857d02b7e2aa4b0') in 0.0091765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.081037 seconds.
  path: Assets/Plugins/FMOD/images/TransportPlayButtonOn.png
  artifactKey: Guid(2d777c9a14189d241aea1afeeeff448c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportPlayButtonOn.png using Guid(2d777c9a14189d241aea1afeeeff448c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6b10e03cfc2ff53d8629a0e7ba8cb20') in 0.004809166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.250950 seconds.
  path: Assets/Plugins/FMOD/images/TransportStopButtonOn.png
  artifactKey: Guid(eab53cb0959d1244aadeacf8b76c755c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportStopButtonOn.png using Guid(eab53cb0959d1244aadeacf8b76c755c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1884bbce2251bcd2e1a405b323d2b0a8') in 0.006451667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.256226 seconds.
  path: Assets/Materials/wattah/water 0399normal.jpg
  artifactKey: Guid(c99fa1ffc45624d60984629801fefed3) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/water 0399normal.jpg using Guid(c99fa1ffc45624d60984629801fefed3) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '427525949462d45ad1391ea6e296f443') in 0.007286416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.462711 seconds.
  path: Assets/Materials/wattah/wawawa.png
  artifactKey: Guid(989c2c83f2fb64b64a706199aa17842d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/wawawa.png using Guid(989c2c83f2fb64b64a706199aa17842d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7c0fd2b6eb838edff68a81f029271786') in 0.007925167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.843707 seconds.
  path: Assets/Plugins/FMOD/images/Preview.png
  artifactKey: Guid(0793eda432fc5df4ab1291e6baacd771) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Preview.png using Guid(0793eda432fc5df4ab1291e6baacd771) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37bee550857e81a72f103ae296128ddb') in 0.00992275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Plugins/FMOD/images/NotFound.png
  artifactKey: Guid(1138ab068176f29499337d7a73dfecd9) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/NotFound.png using Guid(1138ab068176f29499337d7a73dfecd9) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14269d94c60adb7a1c4df7a66c508a41') in 0.003165584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.013157 seconds.
  path: Assets/Plugins/FMOD/images/LevelMeterOff.png
  artifactKey: Guid(48dc5470d93f669419f294fcd33f7b7c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LevelMeterOff.png using Guid(48dc5470d93f669419f294fcd33f7b7c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bb1c1d4e84a0a6fe9f7fae3c6be573f') in 0.004686916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Plugins/FMOD/images/LabeledParameterIcon.png
  artifactKey: Guid(b4d696e5c0be6f44bb2f02aa41320656) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LabeledParameterIcon.png using Guid(b4d696e5c0be6f44bb2f02aa41320656) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00b0aeb6a7c7be69fc0d48691fffb6b8') in 0.005108625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.009089 seconds.
  path: Assets/Plugins/FMOD/images/FolderIconOpen.png
  artifactKey: Guid(d2b54e4f7f80b9448a41d3c5985f5672) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FolderIconOpen.png using Guid(d2b54e4f7f80b9448a41d3c5985f5672) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3ddc215434a51b202e57deed6b8f91') in 0.006359208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.433581 seconds.
  path: Assets/Plugins/FMOD/images/FMODLogoWhite.png
  artifactKey: Guid(8fd8ccb8d7e81d943b28ea7975c7185d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FMODLogoWhite.png using Guid(8fd8ccb8d7e81d943b28ea7975c7185d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eff4c72d6fbb53b34c9a7d6ab814c3d6') in 0.006672917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.016705 seconds.
  path: Assets/Plugins/FMOD/images/EventIcon.png
  artifactKey: Guid(a602f206f9cb31f439c79a2fe23687c5) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/EventIcon.png using Guid(a602f206f9cb31f439c79a2fe23687c5) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d532c97e53015a40463f228cf6f688d') in 0.004108375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.015527 seconds.
  path: Assets/Plugins/FMOD/images/DiscreteParameterIcon.png
  artifactKey: Guid(509563e7079a6ed4cbf3b3240327e702) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/DiscreteParameterIcon.png using Guid(509563e7079a6ed4cbf3b3240327e702) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '93299fc8c9e7a5fae08892252296a9ad') in 0.005090875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.013212 seconds.
  path: Assets/Plugins/FMOD/images/CrossYellow.png
  artifactKey: Guid(348d2265b48c67342a4db2a7062813fa) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/CrossYellow.png using Guid(348d2265b48c67342a4db2a7062813fa) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c52090b16babdfcff4135957dd578d4') in 0.003621292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.025230 seconds.
  path: Assets/Plugins/FMOD/images/ContinuousParameterIcon.png
  artifactKey: Guid(b92803770616fc747bc3c40ffaec0a42) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/ContinuousParameterIcon.png using Guid(b92803770616fc747bc3c40ffaec0a42) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf07c63bc2305db4e2c6d83fc5994820') in 0.004890291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026547 seconds.
  path: Assets/Plugins/FMOD/images/BrowserIcon.png
  artifactKey: Guid(c783b763d12874147876e070661b66ab) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/BrowserIcon.png using Guid(c783b763d12874147876e070661b66ab) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2ce06f22a6668d189c7a7192bd1e9ab') in 0.00505525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026007 seconds.
  path: Assets/Materials/Sprites/box_128.png
  artifactKey: Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/box_128.png using Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04d3ac4e62430873c3aa1df47a87615c') in 0.********* seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.078687 seconds.
  path: Assets/Plugins/FMOD/images/BankIcon.png
  artifactKey: Guid(a7e06068a7215854a84bf5ed8280ed15) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/BankIcon.png using Guid(a7e06068a7215854a84bf5ed8280ed15) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2fa6baf45da87344f0179b77ee181bdd') in 0.********* seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.141773 seconds.
  path: Assets/Plugins/FMOD/images/AddIcon.png
  artifactKey: Guid(3300e81f02e64924eb7cb7782713b126) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/AddIcon.png using Guid(3300e81f02e64924eb7cb7782713b126) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afc1d2cc2b92c8ebfbd8278a9ab7afcf') in 0.******** seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.457655 seconds.
  path: Assets/Materials/wattah/2dwater.png
  artifactKey: Guid(8f70e8844a3c04e50ae9ce8c5bf365c0) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/2dwater.png using Guid(8f70e8844a3c04e50ae9ce8c5bf365c0) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '020b23f9540835e1685311cbbd6fa8fe') in 0.010431125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ec87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.772 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1432ms
	BeginReloadAssembly (307ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (73ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (449ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.2 MB). Loaded Objects now: 6907.
Memory consumption went from 170.2 MB to 166.0 MB.
Total: 7.638041 ms (FindLiveObjects: 0.479417 ms CreateObjectMapping: 0.208917 ms MarkObjects: 5.223083 ms  DeleteObjects: 1.726375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1373ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 6.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6909.
Memory consumption went from 163.6 MB to 161.0 MB.
Total: 13.405709 ms (FindLiveObjects: 1.123958 ms CreateObjectMapping: 0.415792 ms MarkObjects: 9.050875 ms  DeleteObjects: 2.813667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.624 seconds
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.643 seconds
Domain Reload Profiling: 1269ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 6911.
Memory consumption went from 163.5 MB to 161.0 MB.
Total: 12.586958 ms (FindLiveObjects: 0.801625 ms CreateObjectMapping: 2.011458 ms MarkObjects: 7.701334 ms  DeleteObjects: 2.071250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.8 MB). Loaded Objects now: 6912.
Memory consumption went from 153.7 MB to 150.8 MB.
Total: 26.807666 ms (FindLiveObjects: 0.466667 ms CreateObjectMapping: 0.257666 ms MarkObjects: 24.358500 ms  DeleteObjects: 1.723709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.685 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 1398ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6914.
Memory consumption went from 163.5 MB to 160.4 MB.
Total: 7.617167 ms (FindLiveObjects: 0.703834 ms CreateObjectMapping: 0.328417 ms MarkObjects: 4.933250 ms  DeleteObjects: 1.651167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2012.638078 seconds.
  path: Assets/Materials/Lightning.mat
  artifactKey: Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Lightning.mat using Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7d48018efd98d7156d3582a2ccf8c6f') in 0.875206625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  6.738 seconds
Refreshing native plugins compatible for Editor in 160.02 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.863 seconds
Domain Reload Profiling: 7615ms
	BeginReloadAssembly (4365ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (721ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (17ms)
		CreateAndSetChildDomain (1109ms)
	RebuildCommonClasses (132ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (2183ms)
		LoadAssemblies (3492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (490ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (863ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (482ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 6.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6936.
Memory consumption went from 167.8 MB to 165.2 MB.
Total: 53.301833 ms (FindLiveObjects: 1.478000 ms CreateObjectMapping: 0.854333 ms MarkObjects: 46.272500 ms  DeleteObjects: 4.695167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  2.077 seconds
Refreshing native plugins compatible for Editor in 10.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.783 seconds
Domain Reload Profiling: 2867ms
	BeginReloadAssembly (893ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (59ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (266ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (1118ms)
		LoadAssemblies (953ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (483ms)
			TypeCache.Refresh (79ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (367ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (398ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6938.
Memory consumption went from 167.5 MB to 164.4 MB.
Total: 9.207708 ms (FindLiveObjects: 1.156917 ms CreateObjectMapping: 0.346375 ms MarkObjects: 5.916542 ms  DeleteObjects: 1.787000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.523 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 2236ms
	BeginReloadAssembly (815ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (277ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (633ms)
		LoadAssemblies (735ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (710ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (549ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.1 MB). Loaded Objects now: 6940.
Memory consumption went from 167.5 MB to 165.4 MB.
Total: 11.632208 ms (FindLiveObjects: 0.760917 ms CreateObjectMapping: 0.443250 ms MarkObjects: 6.608666 ms  DeleteObjects: 3.818417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.179 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.651 seconds
Domain Reload Profiling: 1832ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (724ms)
		LoadAssemblies (637ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (651ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.1 MB). Loaded Objects now: 6942.
Memory consumption went from 167.5 MB to 163.4 MB.
Total: 7.187375 ms (FindLiveObjects: 0.479042 ms CreateObjectMapping: 0.275500 ms MarkObjects: 4.773625 ms  DeleteObjects: 1.658708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.997 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.043 seconds
Domain Reload Profiling: 3044ms
	BeginReloadAssembly (925ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (81ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (184ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (991ms)
		LoadAssemblies (1026ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (375ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (280ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1043ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (804ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (633ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 3.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.9 MB). Loaded Objects now: 6944.
Memory consumption went from 167.5 MB to 162.6 MB.
Total: 52.853167 ms (FindLiveObjects: 12.908416 ms CreateObjectMapping: 0.520833 ms MarkObjects: 34.632709 ms  DeleteObjects: 4.790125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.075 seconds
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.26 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.703 seconds
Domain Reload Profiling: 1781ms
	BeginReloadAssembly (382ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (184ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (623ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (545ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6946.
Memory consumption went from 167.4 MB to 164.0 MB.
Total: 20.307625 ms (FindLiveObjects: 0.607625 ms CreateObjectMapping: 0.453625 ms MarkObjects: 16.726750 ms  DeleteObjects: 2.518791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.321 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.636 seconds
Domain Reload Profiling: 1960ms
	BeginReloadAssembly (648ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (194ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (610ms)
		LoadAssemblies (678ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (356ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.9 MB). Loaded Objects now: 6948.
Memory consumption went from 167.5 MB to 164.6 MB.
Total: 7.849000 ms (FindLiveObjects: 0.421416 ms CreateObjectMapping: 0.228250 ms MarkObjects: 5.567292 ms  DeleteObjects: 1.630917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.869 seconds
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.721 seconds
Domain Reload Profiling: 1597ms
	BeginReloadAssembly (290ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (517ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (244ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (722ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (557ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.3 MB). Loaded Objects now: 6950.
Memory consumption went from 167.5 MB to 165.2 MB.
Total: 11.073708 ms (FindLiveObjects: 1.405708 ms CreateObjectMapping: 0.434500 ms MarkObjects: 5.581500 ms  DeleteObjects: 3.651541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.712 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.645 seconds
Domain Reload Profiling: 1360ms
	BeginReloadAssembly (247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (645ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.7 MB). Loaded Objects now: 6952.
Memory consumption went from 167.5 MB to 164.8 MB.
Total: 11.948750 ms (FindLiveObjects: 0.583792 ms CreateObjectMapping: 0.292833 ms MarkObjects: 8.725917 ms  DeleteObjects: 2.345916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.778 seconds
Refreshing native plugins compatible for Editor in 3.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.716 seconds
Domain Reload Profiling: 2497ms
	BeginReloadAssembly (673ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (377ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (1004ms)
		LoadAssemblies (911ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (204ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (716ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (521ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6954.
Memory consumption went from 167.5 MB to 164.4 MB.
Total: 7.916459 ms (FindLiveObjects: 0.690125 ms CreateObjectMapping: 0.297667 ms MarkObjects: 5.260250 ms  DeleteObjects: 1.667875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.946 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1735ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (139ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (549ms)
		LoadAssemblies (418ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (215ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (608ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (465ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6956.
Memory consumption went from 167.5 MB to 164.6 MB.
Total: 30.905083 ms (FindLiveObjects: 5.175541 ms CreateObjectMapping: 1.252875 ms MarkObjects: 21.407375 ms  DeleteObjects: 3.068333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.765 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (423ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (667ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (401ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 6958.
Memory consumption went from 167.5 MB to 163.5 MB.
Total: 9.062209 ms (FindLiveObjects: 0.550708 ms CreateObjectMapping: 0.412834 ms MarkObjects: 6.261708 ms  DeleteObjects: 1.836209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.217 seconds
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.647 seconds
Domain Reload Profiling: 1868ms
	BeginReloadAssembly (494ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (659ms)
		LoadAssemblies (629ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (45ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (647ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6960.
Memory consumption went from 167.5 MB to 163.7 MB.
Total: 11.658917 ms (FindLiveObjects: 0.783791 ms CreateObjectMapping: 0.598209 ms MarkObjects: 8.304542 ms  DeleteObjects: 1.971750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.334 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.689 seconds
Domain Reload Profiling: 2026ms
	BeginReloadAssembly (557ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (162ms)
	RebuildCommonClasses (97ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (620ms)
		LoadAssemblies (548ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (689ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (535ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6962.
Memory consumption went from 167.5 MB to 163.6 MB.
Total: 7.547666 ms (FindLiveObjects: 0.481750 ms CreateObjectMapping: 0.210375 ms MarkObjects: 5.163750 ms  DeleteObjects: 1.690917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.669 seconds
Refreshing native plugins compatible for Editor in 3.25 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.32 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.623 seconds
Domain Reload Profiling: 1295ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (385ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (624ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (471ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.1 MB). Loaded Objects now: 6964.
Memory consumption went from 167.5 MB to 163.4 MB.
Total: 9.840917 ms (FindLiveObjects: 1.294833 ms CreateObjectMapping: 0.961333 ms MarkObjects: 5.827250 ms  DeleteObjects: 1.756459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.872 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.631 seconds
Domain Reload Profiling: 1507ms
	BeginReloadAssembly (246ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (632ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (345ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.7 MB). Loaded Objects now: 6966.
Memory consumption went from 167.5 MB to 162.8 MB.
Total: 10.934584 ms (FindLiveObjects: 1.235958 ms CreateObjectMapping: 0.348375 ms MarkObjects: 6.615250 ms  DeleteObjects: 2.734542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.636 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.580 seconds
Domain Reload Profiling: 1218ms
	BeginReloadAssembly (215ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (580ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6968.
Memory consumption went from 167.5 MB to 163.7 MB.
Total: 11.424458 ms (FindLiveObjects: 0.466125 ms CreateObjectMapping: 0.297292 ms MarkObjects: 7.583917 ms  DeleteObjects: 3.076250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.858 seconds
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.762 seconds
Domain Reload Profiling: 1624ms
	BeginReloadAssembly (267ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (391ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (215ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (416ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6970.
Memory consumption went from 167.5 MB to 164.5 MB.
Total: 16.143917 ms (FindLiveObjects: 0.605666 ms CreateObjectMapping: 0.401292 ms MarkObjects: 12.205125 ms  DeleteObjects: 2.931083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.701 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.608 seconds
Domain Reload Profiling: 1313ms
	BeginReloadAssembly (293ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (180ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (170ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (145ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (609ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (468ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.7 MB). Loaded Objects now: 6972.
Memory consumption went from 167.5 MB to 161.8 MB.
Total: 9.489166 ms (FindLiveObjects: 0.591917 ms CreateObjectMapping: 0.214333 ms MarkObjects: 6.146834 ms  DeleteObjects: 2.533959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  1.142 seconds
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.651 seconds
Domain Reload Profiling: 1797ms
	BeginReloadAssembly (450ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (600ms)
		LoadAssemblies (524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.1 MB). Loaded Objects now: 6974.
Memory consumption went from 167.5 MB to 162.5 MB.
Total: 17.036000 ms (FindLiveObjects: 3.166209 ms CreateObjectMapping: 0.767500 ms MarkObjects: 9.482542 ms  DeleteObjects: 3.618500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.786 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.625 seconds
Domain Reload Profiling: 1414ms
	BeginReloadAssembly (215ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6976.
Memory consumption went from 167.5 MB to 163.7 MB.
Total: 20.781166 ms (FindLiveObjects: 0.525250 ms CreateObjectMapping: 0.388917 ms MarkObjects: 16.304167 ms  DeleteObjects: 3.562125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.767 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.77 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.841 seconds
Domain Reload Profiling: 1612ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (150ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (842ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (643ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6978.
Memory consumption went from 167.6 MB to 164.2 MB.
Total: 17.658291 ms (FindLiveObjects: 0.510000 ms CreateObjectMapping: 0.625000 ms MarkObjects: 13.057833 ms  DeleteObjects: 3.463666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.930 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 1646ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (608ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (334ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (276ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (552ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (416ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.5 MB). Loaded Objects now: 6980.
Memory consumption went from 167.6 MB to 162.1 MB.
Total: 19.274541 ms (FindLiveObjects: 0.686375 ms CreateObjectMapping: 0.357958 ms MarkObjects: 14.150958 ms  DeleteObjects: 4.078875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.770 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.648 seconds
Domain Reload Profiling: 1422ms
	BeginReloadAssembly (260ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (125ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (215ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (648ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6982.
Memory consumption went from 167.6 MB to 163.7 MB.
Total: 17.697042 ms (FindLiveObjects: 0.819125 ms CreateObjectMapping: 0.473500 ms MarkObjects: 13.339125 ms  DeleteObjects: 3.064417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.715 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.635 seconds
Domain Reload Profiling: 1354ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (432ms)
		LoadAssemblies (241ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 6984.
Memory consumption went from 167.6 MB to 163.5 MB.
Total: 8.879000 ms (FindLiveObjects: 0.579709 ms CreateObjectMapping: 0.279000 ms MarkObjects: 5.768000 ms  DeleteObjects: 2.251958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.771 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.45 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.627 seconds
Domain Reload Profiling: 1401ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (481ms)
		LoadAssemblies (270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (627ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (487ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6986.
Memory consumption went from 167.5 MB to 163.0 MB.
Total: 18.108958 ms (FindLiveObjects: 0.782792 ms CreateObjectMapping: 0.481792 ms MarkObjects: 13.642625 ms  DeleteObjects: 3.200750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.741 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.33 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.651 seconds
Domain Reload Profiling: 1397ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (440ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (238ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6988.
Memory consumption went from 167.6 MB to 163.0 MB.
Total: 17.047584 ms (FindLiveObjects: 1.184333 ms CreateObjectMapping: 0.555000 ms MarkObjects: 11.779000 ms  DeleteObjects: 3.528042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.819 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.632 seconds
Domain Reload Profiling: 1454ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (293ms)
			TypeCache.Refresh (43ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (236ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (632ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (372ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.1 MB). Loaded Objects now: 6990.
Memory consumption went from 167.6 MB to 163.5 MB.
Total: 10.580750 ms (FindLiveObjects: 0.612250 ms CreateObjectMapping: 0.370917 ms MarkObjects: 7.230584 ms  DeleteObjects: 2.366292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.698 seconds
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.615 seconds
Domain Reload Profiling: 1317ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (426ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (202ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6992.
Memory consumption went from 167.6 MB to 164.6 MB.
Total: 26.727333 ms (FindLiveObjects: 1.998667 ms CreateObjectMapping: 0.406583 ms MarkObjects: 21.098333 ms  DeleteObjects: 3.223042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.880 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.78 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.644 seconds
Domain Reload Profiling: 1528ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (586ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (353ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (645ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (487ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 6994.
Memory consumption went from 167.6 MB to 163.9 MB.
Total: 8.463750 ms (FindLiveObjects: 0.507542 ms CreateObjectMapping: 0.390584 ms MarkObjects: 5.494916 ms  DeleteObjects: 2.070125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.704 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1325ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (43ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (619ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (6.2 MB). Loaded Objects now: 6996.
Memory consumption went from 167.5 MB to 161.3 MB.
Total: 12.287792 ms (FindLiveObjects: 0.665042 ms CreateObjectMapping: 0.365833 ms MarkObjects: 6.432708 ms  DeleteObjects: 4.823667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.870 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.652 seconds
Domain Reload Profiling: 1525ms
	BeginReloadAssembly (273ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (529ms)
		LoadAssemblies (369ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (515ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (389ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.1 MB). Loaded Objects now: 6998.
Memory consumption went from 167.6 MB to 163.5 MB.
Total: 8.571292 ms (FindLiveObjects: 0.492334 ms CreateObjectMapping: 0.389791 ms MarkObjects: 5.595167 ms  DeleteObjects: 2.093459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.693 seconds
Refreshing native plugins compatible for Editor in 2.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.633 seconds
Domain Reload Profiling: 1330ms
	BeginReloadAssembly (240ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (125ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 5.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.8 MB). Loaded Objects now: 7000.
Memory consumption went from 167.6 MB to 162.8 MB.
Total: 10.925959 ms (FindLiveObjects: 1.409666 ms CreateObjectMapping: 0.314334 ms MarkObjects: 5.114166 ms  DeleteObjects: 4.087125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.654 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.42 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.611 seconds
Domain Reload Profiling: 1270ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (390ms)
		LoadAssemblies (241ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.9 MB). Loaded Objects now: 7002.
Memory consumption went from 167.6 MB to 164.7 MB.
Total: 10.565500 ms (FindLiveObjects: 1.293209 ms CreateObjectMapping: 0.436750 ms MarkObjects: 7.155583 ms  DeleteObjects: 1.679417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.716 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.17 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 1336ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (264ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 7004.
Memory consumption went from 167.6 MB to 163.9 MB.
Total: 18.961167 ms (FindLiveObjects: 1.260000 ms CreateObjectMapping: 0.424083 ms MarkObjects: 14.403458 ms  DeleteObjects: 2.873292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 19844.691703 seconds.
  path: Assets/Materials/SoulCreature1.mat
  artifactKey: Guid(fe6d92738a30547ea8908f54d5ee931e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SoulCreature1.mat using Guid(fe6d92738a30547ea8908f54d5ee931e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1761ddbed2903cdb59fd309107a4c7d') in 0.413136708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.787 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.55 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 1404ms
	BeginReloadAssembly (298ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (143ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (431ms)
		LoadAssemblies (284ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (615ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (472ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 7026.
Memory consumption went from 171.9 MB to 168.5 MB.
Total: 11.184750 ms (FindLiveObjects: 1.443166 ms CreateObjectMapping: 0.556709 ms MarkObjects: 6.464167 ms  DeleteObjects: 2.720166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.713 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.621 seconds
Domain Reload Profiling: 1338ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (444ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 7028.
Memory consumption went from 171.6 MB to 167.1 MB.
Total: 9.204500 ms (FindLiveObjects: 0.837959 ms CreateObjectMapping: 0.348584 ms MarkObjects: 5.746958 ms  DeleteObjects: 2.270417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.700 seconds
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.691 seconds
Domain Reload Profiling: 1394ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (259ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (200ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (691ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (413ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 4.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.6 MB). Loaded Objects now: 7030.
Memory consumption went from 171.6 MB to 167.0 MB.
Total: 21.001167 ms (FindLiveObjects: 2.165000 ms CreateObjectMapping: 0.985958 ms MarkObjects: 15.353167 ms  DeleteObjects: 2.496042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.738 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.610 seconds
Domain Reload Profiling: 1352ms
	BeginReloadAssembly (234ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (444ms)
		LoadAssemblies (270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 7032.
Memory consumption went from 171.6 MB to 168.4 MB.
Total: 8.560375 ms (FindLiveObjects: 0.611833 ms CreateObjectMapping: 0.322708 ms MarkObjects: 5.643084 ms  DeleteObjects: 1.982167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.705 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.617 seconds
Domain Reload Profiling: 1325ms
	BeginReloadAssembly (231ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (419ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (202ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.4 MB). Loaded Objects now: 7034.
Memory consumption went from 171.6 MB to 167.3 MB.
Total: 14.147208 ms (FindLiveObjects: 0.907750 ms CreateObjectMapping: 0.472458 ms MarkObjects: 8.994083 ms  DeleteObjects: 3.772000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.726 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 1346ms
	BeginReloadAssembly (241ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (428ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 7036.
Memory consumption went from 171.6 MB to 168.2 MB.
Total: 13.085375 ms (FindLiveObjects: 0.807125 ms CreateObjectMapping: 0.415084 ms MarkObjects: 7.991791 ms  DeleteObjects: 3.870583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.748 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.625 seconds
Domain Reload Profiling: 1380ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (471ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (479ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 7038.
Memory consumption went from 171.6 MB to 168.6 MB.
Total: 15.635500 ms (FindLiveObjects: 0.653584 ms CreateObjectMapping: 0.476250 ms MarkObjects: 10.853375 ms  DeleteObjects: 3.651666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.669 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.26 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.588 seconds
Domain Reload Profiling: 1261ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (386ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (145ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (589ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (454ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.3 MB). Loaded Objects now: 7040.
Memory consumption went from 171.6 MB to 167.4 MB.
Total: 12.635500 ms (FindLiveObjects: 0.536000 ms CreateObjectMapping: 0.235416 ms MarkObjects: 9.134250 ms  DeleteObjects: 2.729208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.926 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.737 seconds
Domain Reload Profiling: 1668ms
	BeginReloadAssembly (281ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (428ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (738ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (435ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (5.0 MB). Loaded Objects now: 7042.
Memory consumption went from 171.6 MB to 166.6 MB.
Total: 10.294333 ms (FindLiveObjects: 0.464916 ms CreateObjectMapping: 0.425875 ms MarkObjects: 5.957833 ms  DeleteObjects: 3.445000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16b27b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.958 seconds
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.022 seconds
Domain Reload Profiling: 1984ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (568ms)
		LoadAssemblies (410ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1022ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (713ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (557ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 7044.
Memory consumption went from 171.6 MB to 167.7 MB.
Total: 16.700000 ms (FindLiveObjects: 0.503500 ms CreateObjectMapping: 0.411042 ms MarkObjects: 13.491417 ms  DeleteObjects: 2.293541 ms)

Prepare: number of updated asset objects reloaded= 0
