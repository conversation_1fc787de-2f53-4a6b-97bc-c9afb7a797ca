Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
53137
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3575629211 [EditorId] 3575629211 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 20.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56028
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.000906 seconds.
- Loaded All Assemblies, in  0.478 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.441 seconds
Domain Reload Profiling: 919ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (212ms)
		LoadAssemblies (148ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (205ms)
				TypeCache.ScanAssembly (182ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (441ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (383ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (145ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Asset path could not be found for script compilation file '/Assets/LightningAudio.cs'
- Loaded All Assemblies, in  1.049 seconds
Refreshing native plugins compatible for Editor in 4.04 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.009 seconds
Domain Reload Profiling: 2058ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (707ms)
		LoadAssemblies (405ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (352ms)
			TypeCache.Refresh (278ms)
				TypeCache.ScanAssembly (257ms)
			BuildScriptInfoCaches (56ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1010ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (806ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.31 seconds
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (3.1 MB). Loaded Objects now: 6819.
Memory consumption went from 172.5 MB to 169.4 MB.
Total: 36.680291 ms (FindLiveObjects: 3.324958 ms CreateObjectMapping: 0.520416 ms MarkObjects: 29.165833 ms  DeleteObjects: 3.667500 ms)

========================================================================
Received Import Request.
  Time since last request: 202893.241335 seconds.
  path: Assets/Scripts/LightningAudio.cs
  artifactKey: Guid(a60c0fff3bd444a9d9462ec6a9232ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LightningAudio.cs using Guid(a60c0fff3bd444a9d9462ec6a9232ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd9d3f48f8aced57ebffeb7ac555c8589') in 0.007597583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x3275fb000 may have been prematurely finalized
- Loaded All Assemblies, in  1.000 seconds
Refreshing native plugins compatible for Editor in 3.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.026 seconds
Domain Reload Profiling: 2030ms
	BeginReloadAssembly (298ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (626ms)
		LoadAssemblies (458ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1026ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (766ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (565ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6836.
Memory consumption went from 163.7 MB to 160.7 MB.
Total: 13.590250 ms (FindLiveObjects: 1.073083 ms CreateObjectMapping: 0.550125 ms MarkObjects: 9.146833 ms  DeleteObjects: 2.819166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 8.572762 seconds.
  path: Assets/Scripts/LightningAudio.cs
  artifactKey: Guid(a60c0fff3bd444a9d9462ec6a9232ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LightningAudio.cs using Guid(a60c0fff3bd444a9d9462ec6a9232ea3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af5517636c3436c91faf22dc5945dd13') in 0.0033865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e887000 may have been prematurely finalized
- Loaded All Assemblies, in  1.184 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1846ms
	BeginReloadAssembly (414ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (687ms)
		LoadAssemblies (551ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (279ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (368ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6838.
Memory consumption went from 162.0 MB to 158.6 MB.
Total: 10.860917 ms (FindLiveObjects: 2.026209 ms CreateObjectMapping: 0.427750 ms MarkObjects: 6.538166 ms  DeleteObjects: 1.867666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e887000 may have been prematurely finalized
- Loaded All Assemblies, in  0.843 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.683 seconds
Domain Reload Profiling: 1529ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (684ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (393ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.42 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6840.
Memory consumption went from 161.9 MB to 158.6 MB.
Total: 6.766125 ms (FindLiveObjects: 0.394375 ms CreateObjectMapping: 0.304125 ms MarkObjects: 4.800459 ms  DeleteObjects: 1.266583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d487000 may have been prematurely finalized
- Loaded All Assemblies, in  0.942 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.59 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.784 seconds
Domain Reload Profiling: 1729ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (545ms)
		LoadAssemblies (423ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (622ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6842.
Memory consumption went from 161.9 MB to 158.6 MB.
Total: 10.345334 ms (FindLiveObjects: 0.569792 ms CreateObjectMapping: 0.497291 ms MarkObjects: 7.536917 ms  DeleteObjects: 1.740292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d487000 may have been prematurely finalized
- Loaded All Assemblies, in  1.394 seconds
Refreshing native plugins compatible for Editor in 2.48 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.700 seconds
Domain Reload Profiling: 2097ms
	BeginReloadAssembly (545ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (132ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (754ms)
		LoadAssemblies (720ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (214ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (700ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (393ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6844.
Memory consumption went from 161.8 MB to 159.2 MB.
Total: 10.745250 ms (FindLiveObjects: 0.521666 ms CreateObjectMapping: 0.283792 ms MarkObjects: 7.924042 ms  DeleteObjects: 2.014750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  1.106 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.706 seconds
Domain Reload Profiling: 1817ms
	BeginReloadAssembly (394ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (129ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (608ms)
		LoadAssemblies (485ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (706ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (528ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (382ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6846.
Memory consumption went from 161.9 MB to 159.1 MB.
Total: 7.339209 ms (FindLiveObjects: 0.468125 ms CreateObjectMapping: 0.396166 ms MarkObjects: 5.098917 ms  DeleteObjects: 1.375542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  1.012 seconds
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.69 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.834 seconds
Domain Reload Profiling: 1849ms
	BeginReloadAssembly (301ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (128ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (402ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (262ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (835ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (662ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (509ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6848.
Memory consumption went from 161.9 MB to 158.4 MB.
Total: 14.462042 ms (FindLiveObjects: 0.564375 ms CreateObjectMapping: 0.723125 ms MarkObjects: 10.775125 ms  DeleteObjects: 2.398791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.861 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.751 seconds
Domain Reload Profiling: 1615ms
	BeginReloadAssembly (247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (527ms)
		LoadAssemblies (324ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (259ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (751ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 4.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.5 MB). Loaded Objects now: 6850.
Memory consumption went from 161.9 MB to 157.4 MB.
Total: 21.560875 ms (FindLiveObjects: 1.669458 ms CreateObjectMapping: 1.115333 ms MarkObjects: 16.727083 ms  DeleteObjects: 2.047708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.782 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.76 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.865 seconds
Domain Reload Profiling: 1650ms
	BeginReloadAssembly (273ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (441ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (641ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.5 MB). Loaded Objects now: 6852.
Memory consumption went from 161.9 MB to 159.3 MB.
Total: 11.540125 ms (FindLiveObjects: 0.605542 ms CreateObjectMapping: 0.476500 ms MarkObjects: 8.956375 ms  DeleteObjects: 1.500500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.740 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.78 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.840 seconds
Domain Reload Profiling: 1584ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (432ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (841ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (633ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (456ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.9 MB). Loaded Objects now: 6854.
Memory consumption went from 161.8 MB to 158.9 MB.
Total: 12.356375 ms (FindLiveObjects: 0.664125 ms CreateObjectMapping: 0.398708 ms MarkObjects: 8.872500 ms  DeleteObjects: 2.417333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.756 seconds
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.699 seconds
Domain Reload Profiling: 1459ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (465ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (701ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (542ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (398ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6856.
Memory consumption went from 161.9 MB to 158.0 MB.
Total: 6.922083 ms (FindLiveObjects: 0.543708 ms CreateObjectMapping: 0.422834 ms MarkObjects: 4.594625 ms  DeleteObjects: 1.360083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2578.146230 seconds.
  path: Assets/Prefabs/SoulCreature1_Tutorial.prefab
  artifactKey: Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature1_Tutorial.prefab using Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bbf1e4d6624d1e22c125a425585acd53') in 0.633259875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 17.343658 seconds.
  path: Assets/Prefabs/SoulCreature1_Tutorial.prefab
  artifactKey: Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature1_Tutorial.prefab using Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fb4528aeeb009b996de8bb68c2736435') in 0.044590958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 6.906188 seconds.
  path: Assets/Prefabs/SoulCreature1_Tutorial.prefab
  artifactKey: Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature1_Tutorial.prefab using Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a5a7b956c7b6aa03c215007eaa39c44b') in 0.013032375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 11468.667789 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c79f3403a68f4593046b56740472d7ec') in 0.220702708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 14.473881 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0f79dbc7421b843abf9443ad005ce13') in 0.050046417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 5.199156 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '09349bff2f8d51ecd1f63244e05ba673') in 0.019678208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.542868 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b32947a0fd0b28733b6662a015cdc4e') in 0.010842917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 4.273389 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2ad980989659a207d2e0b952896a4698') in 0.031925417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 6.747246 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd77d0a65d7bbf4fc2ce7e8839becdd4') in 0.012531792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 2.142958 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f93aec691584fc4472785a3f4feb8106') in 0.018728916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 3.243280 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '078b4558f62e6c037e39774bc4228a14') in 0.012315042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 2.232059 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4a955f7e2f1725c236cee7d286e986dc') in 0.007871958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 2.295706 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6fd147f5b463fbefd1666d52d6f1d5e8') in 0.011801417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.457306 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13af965e65bdfc4900f90979843fdbcf') in 0.015165459 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.344186 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b4b03c844650fa14c8efd5c28a4087c9') in 0.012088209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 2.034379 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ddab14fa0b5094b2eafc646532a2d2c6') in 0.01420875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.625612 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '357b22d8b3dd742503672844e0fb1d5e') in 0.008743292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 3.387948 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9f84fdffc6847b3847ef6bccccf4bae') in 0.016756292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 8.414609 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3927ff541e2f0066b2fd9f3cc4bf6300') in 0.031970209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 3.754608 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4777e4c90fdfa0af3c2a95168872d9a') in 0.015062916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  1.010 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.934 seconds
Domain Reload Profiling: 1948ms
	BeginReloadAssembly (394ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (433ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (235ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (935ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (731ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (528ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.7 MB). Loaded Objects now: 6891.
Memory consumption went from 168.9 MB to 166.3 MB.
Total: 17.168792 ms (FindLiveObjects: 1.475500 ms CreateObjectMapping: 0.473000 ms MarkObjects: 12.619000 ms  DeleteObjects: 2.600500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 16.833720 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8666eef4ac18419c40fda878f2a7a4d8') in 0.413167375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 14.650714 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '364b09c11e16e304907280adc14aea63') in 0.068138209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.701355 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a93e0c0a62be8296068bb6d8a46a766') in 0.016233792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.626647 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cdf5520765030f3cbef98f5917c60c4d') in 0.0096565 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.763 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.693 seconds
Domain Reload Profiling: 1460ms
	BeginReloadAssembly (254ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (693ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (537ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.2 MB). Loaded Objects now: 6913.
Memory consumption went from 173.0 MB to 169.7 MB.
Total: 7.946000 ms (FindLiveObjects: 0.553375 ms CreateObjectMapping: 0.356083 ms MarkObjects: 5.321833 ms  DeleteObjects: 1.713833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 351.127450 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9721066f2ccaab3ba63ff3bfb6e38a97') in 0.449704584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.793 seconds
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 1494ms
	BeginReloadAssembly (265ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (541ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (401ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6935.
Memory consumption went from 177.0 MB to 173.1 MB.
Total: 8.499500 ms (FindLiveObjects: 0.655375 ms CreateObjectMapping: 0.341708 ms MarkObjects: 6.054292 ms  DeleteObjects: 1.447500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.2 MB). Loaded Objects now: 6935.
Memory consumption went from 167.1 MB to 164.9 MB.
Total: 31.910625 ms (FindLiveObjects: 0.580542 ms CreateObjectMapping: 0.297917 ms MarkObjects: 28.708333 ms  DeleteObjects: 2.322292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.756 seconds
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.675 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (271ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (675ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6937.
Memory consumption went from 176.6 MB to 173.8 MB.
Total: 13.367458 ms (FindLiveObjects: 0.537875 ms CreateObjectMapping: 0.322458 ms MarkObjects: 10.923417 ms  DeleteObjects: 1.582958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.742 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.910 seconds
Domain Reload Profiling: 1654ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (431ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (910ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (669ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (487ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6939.
Memory consumption went from 176.6 MB to 174.0 MB.
Total: 9.426709 ms (FindLiveObjects: 0.467417 ms CreateObjectMapping: 0.473042 ms MarkObjects: 6.667458 ms  DeleteObjects: 1.817750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 729.184110 seconds.
  path: Assets/Materials/Lightning.mat
  artifactKey: Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Lightning.mat using Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '207d2dc4feab64975d79b380bfa1bf04') in 0.709303083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.807 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.753 seconds
Domain Reload Profiling: 1563ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (753ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (598ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.3 MB). Loaded Objects now: 6994.
Memory consumption went from 181.3 MB to 179.0 MB.
Total: 15.316959 ms (FindLiveObjects: 2.236583 ms CreateObjectMapping: 0.448292 ms MarkObjects: 10.295333 ms  DeleteObjects: 2.336208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 358.365410 seconds.
  path: Assets/Prefabs/BackpackParticlesHalo.prefab
  artifactKey: Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BackpackParticlesHalo.prefab using Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '24670107bf71d8b60aa0be97d701a6c9') in 0.430198709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 5.118922 seconds.
  path: Assets/Prefabs/BackpackParticlesHalo.prefab
  artifactKey: Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BackpackParticlesHalo.prefab using Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acf9e2d22c5917c252e6b307ac5503c0') in 0.032996875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.752 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1437ms
	BeginReloadAssembly (264ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (412ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (682ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 7016.
Memory consumption went from 185.3 MB to 181.4 MB.
Total: 6.945167 ms (FindLiveObjects: 0.507542 ms CreateObjectMapping: 0.303500 ms MarkObjects: 4.713375 ms  DeleteObjects: 1.420084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 160.608096 seconds.
  path: Assets/Prefabs/BackpackParticlesHalo.prefab
  artifactKey: Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BackpackParticlesHalo.prefab using Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c288fabac1adc53860b933a23ef58b43') in 0.4180965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 5.196209 seconds.
  path: Assets/Prefabs/BackpackParticlesHalo.prefab
  artifactKey: Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BackpackParticlesHalo.prefab using Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7191bb1984a56b47d043b954b13dc66b') in 0.008862542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.801 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.686 seconds
Domain Reload Profiling: 1492ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (71ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (465ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (396ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 7038.
Memory consumption went from 189.3 MB to 185.4 MB.
Total: 7.625541 ms (FindLiveObjects: 0.417875 ms CreateObjectMapping: 0.309584 ms MarkObjects: 5.230541 ms  DeleteObjects: 1.667000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 95.806292 seconds.
  path: Assets/Prefabs/BackpackParticlesHalo.prefab
  artifactKey: Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BackpackParticlesHalo.prefab using Guid(8f60fd99fe01e48728591100d4c20bc7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6bf71bf11e3ef1a84e34a57f04c3940') in 0.389297459 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.826 seconds
Refreshing native plugins compatible for Editor in 10.14 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.53 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.271 seconds
Domain Reload Profiling: 2110ms
	BeginReloadAssembly (268ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (483ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (189ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1272ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1059ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (219ms)
			ProcessInitializeOnLoadAttributes (752ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 7060.
Memory consumption went from 193.3 MB to 190.0 MB.
Total: 14.036250 ms (FindLiveObjects: 0.442292 ms CreateObjectMapping: 0.479042 ms MarkObjects: 10.231291 ms  DeleteObjects: 2.882917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d257000 may have been prematurely finalized
- Loaded All Assemblies, in  0.677 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1352ms
	BeginReloadAssembly (223ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.3 MB). Loaded Objects now: 7062.
Memory consumption went from 193.0 MB to 188.7 MB.
Total: 8.926708 ms (FindLiveObjects: 0.451375 ms CreateObjectMapping: 0.250666 ms MarkObjects: 5.998625 ms  DeleteObjects: 2.225542 ms)

Prepare: number of updated asset objects reloaded= 0
