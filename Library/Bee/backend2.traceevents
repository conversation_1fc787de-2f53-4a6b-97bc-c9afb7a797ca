{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1748871699574212, "dur":456375, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871700030614, "dur":1820, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871700032496, "dur":85, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1748871700032582, "dur":122, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871700033054, "dur":14827, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700048263, "dur":14528, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700063110, "dur":324, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700063828, "dur":161, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700064314, "dur":2125, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700066496, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700066890, "dur":113, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700067375, "dur":124, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748871700068629, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748871700070067, "dur":10561, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748871700080855, "dur":95, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748871700081092, "dur":113, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748871700081515, "dur":114, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748871700081868, "dur":1004, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748871700083201, "dur":9108, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748871700092619, "dur":908, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":0, "ts":1748871700093667, "dur":667, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748871700094384, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748871700095121, "dur":17828, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Cursor.Editor.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748871700113134, "dur":237, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700114566, "dur":114, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll" }}
,{ "pid":12345, "tid":0, "ts":1748871700114683, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700115445, "dur":145, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748871700115592, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":0, "ts":1748871700115667, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700115895, "dur":198, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748871700116133, "dur":105, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll" }}
,{ "pid":12345, "tid":0, "ts":1748871700116240, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700116317, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748871700116386, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748871700116463, "dur":97, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748871700116562, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748871700116616, "dur":11343, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748871700128531, "dur":2683, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748871700132251, "dur":636, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700134682, "dur":886, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1748871700032711, "dur":105635, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871700138354, "dur":5630077, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871705768557, "dur":98, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871705768679, "dur":1784, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1748871700034839, "dur":103545, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700138918, "dur":881, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1748871700139800, "dur":1969, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":1, "ts":1748871700141769, "dur":629, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":1, "ts":1748871700138406, "dur":3993, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700142399, "dur":2271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700144670, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700144947, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700145243, "dur":1079, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700146328, "dur":741, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700147070, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700147522, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700147674, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700147833, "dur":950, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700148783, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700149162, "dur":4644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700153868, "dur":2472, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700156347, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700156695, "dur":960, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700157667, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700157795, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700157884, "dur":10273, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700168157, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700168302, "dur":91597, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700259900, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700260062, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700260178, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700260274, "dur":19782, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700280057, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700280161, "dur":117800, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700397963, "dur":2067, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700400051, "dur":678, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700400730, "dur":1695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700402443, "dur":8119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700410587, "dur":527, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700411115, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700411261, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700411442, "dur":846, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700412568, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748871700412814, "dur":34443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700447257, "dur":7557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700454815, "dur":679, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748871700455517, "dur":4990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":1, "ts":1748871700460509, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700460707, "dur":11042, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701064838, "dur":7838, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871700471756, "dur":601143, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":1, "ts":1748871701078332, "dur":2976, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701081313, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701081498, "dur":36476, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701117975, "dur":1213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701119200, "dur":17152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701136354, "dur":1192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701137558, "dur":3755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701141313, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701141370, "dur":36112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701177484, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701177797, "dur":12966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1748871701190825, "dur":1085, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748871701191910, "dur":4576282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700034866, "dur":103695, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700138636, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700139038, "dur":85, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700139165, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700139291, "dur":1231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700140522, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700140587, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700140700, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700140784, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700140920, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700141033, "dur":1963, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700143001, "dur":1968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700144969, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700145535, "dur":898, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700146433, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700147110, "dur":489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700147599, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700147683, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700147891, "dur":949, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700148841, "dur":860, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700149711, "dur":6566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700156277, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700156382, "dur":452, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700156834, "dur":868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700157719, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700157791, "dur":694, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700158486, "dur":1921, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700160417, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700160526, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700160603, "dur":18712, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700179317, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700179415, "dur":77532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700256948, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700257106, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700257197, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700257303, "dur":1833, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700259137, "dur":1045, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700260182, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":2, "ts":1748871700260233, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700260318, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb" }}
,{ "pid":12345, "tid":2, "ts":1748871700260398, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700260470, "dur":870, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700261340, "dur":1349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700263329, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeComponentCopyPaste.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700264355, "dur":1146, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/Drawers/Vector4ParameterDrawer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700265501, "dur":2664, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/Drawers/TextureParameterDrawer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700268165, "dur":3449, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/Drawers/IntParameterDrawer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700271966, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/Drawers/ColorParameterDrawer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700272750, "dur":1603, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/Drawers/BoolParameterDrawer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700274353, "dur":2355, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Utilities/TimedScope.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700276709, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Utilities/SerializedBitArray.deprecated.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700277529, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Utilities/SerializedBitArray.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700278837, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Utilities/LocalizationHelper.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700279406, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Utilities/EditorMaterialQuality.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700262690, "dur":17442, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700280630, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Debugging/DebugState.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700281232, "dur":1019, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/CoreRenderPipelinePreferences.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700282251, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/CoreEditorUtils.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700280132, "dur":3851, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700284148, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/FrameData/UniversalResourceData.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700284942, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/FrameData/UniversalResourceBase.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700286302, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/FrameData/UniversalCameraData.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700283983, "dur":3557, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700288809, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Invocations/InvocationInspector.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700287540, "dur":2863, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700291167, "dur":862, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/GPUDriven/OcclusionCullingCommonShaderVariables.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700292029, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/GPUDriven/OcclusionCullingCommon.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700292688, "dur":780, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/GPUDriven/OccluderDepthPyramidConstants.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700290403, "dur":3221, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700295188, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector4/Vector4MoveTowards.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700296050, "dur":923, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector4/Vector4Minimum.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700293625, "dur":4818, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700298661, "dur":837, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Graph/GetGraphs.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700299701, "dur":1983, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Formula.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700302442, "dur":2235, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics2D/TriggerEvent2DUnit.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700304905, "dur":1984, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics2D/OnCollisionExit2D.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700306889, "dur":3021, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics2D/OnCollisionEnter2D.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700309911, "dur":1951, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics2D/CollisionEvent2DUnit.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700311863, "dur":979, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics/TriggerEventUnit.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700298443, "dur":14501, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700314121, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Codebase/InvokeMember.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700314697, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Codebase/GetMember.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700315689, "dur":887, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Flow.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700316576, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/EditorBinding/UnitTitleAttribute.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700318069, "dur":999, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/EditorBinding/UnitShortTitleAttribute.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700312944, "dur":6822, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700319766, "dur":1743, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700321509, "dur":3223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700325534, "dur":994, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Description/MachineDescription.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700326723, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Description/IMachineDescription.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700329409, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/ReorderableList/ReorderableListStyles.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700329972, "dur":1634, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/ReorderableList/ReorderableListGUI.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700324733, "dur":7520, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700332253, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Volume/KeyframeUtility.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700333670, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/SceneRenderPipeline.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700335718, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/HashFNV1A32.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700337920, "dur":995, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/GPUPrefixSum/GPUPrefixSum.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700338916, "dur":808, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/FSRUtils.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700332253, "dur":7472, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700339726, "dur":951, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700340678, "dur":4823, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.Binding.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700345502, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeIndexOfIndices.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700347193, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Deprecated.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700348121, "dur":906, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/VolumeDebugSettings.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700349028, "dur":4154, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/ShaderDebugPrintManager.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700339726, "dur":14376, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700354352, "dur":1006, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Utilities/CSharpNameUtility.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700354102, "dur":2623, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700356776, "dur":1116, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/AmbiguousOperatorException.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700357892, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/AdditionHandler.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700359311, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/MemberInfoComparer.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700359863, "dur":1062, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/MemberFilter.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700361444, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/IPrewarmable.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700356725, "dur":5979, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700363963, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/EditorBinding/TypeSetAttribute.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700362704, "dur":2419, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700365330, "dur":2028, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections/xxHash3.AVX2.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700365124, "dur":3530, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700368816, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/AssetUpgrade/AnimationTrackUpgrade.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700368655, "dur":3272, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700371928, "dur":1088, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700373017, "dur":2006, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownDataSource.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700375024, "dur":1356, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdown.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700376769, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/InputSystemPackageControl.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700377403, "dur":2710, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/InputParameterEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700380701, "dur":930, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Debugger/InputDebuggerWindow.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700381632, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Debugger/InputActionDebuggerWindow.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700382302, "dur":3471, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/ControlPicker/Layouts/TouchscreenControlPickerLayout.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700385773, "dur":2372, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/ControlPicker/Layouts/DefaultInputControlPickerLayout.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700373017, "dur":15547, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700388962, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Actions/InputInteractionContext.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700389688, "dur":1016, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Actions/InputBindingResolver.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700388564, "dur":3661, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700393027, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/PendingChangesViewPendingChangeMenu.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700392225, "dur":2072, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700395005, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UI/Tree/GetChangesOverlayIcon.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700396128, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UI/TabButton.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700394297, "dur":3438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700398060, "dur":1219, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Configuration/CloudEdition/Welcome/AutoLogin.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700399280, "dur":680, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Configuration/ClientConfiguration.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700399960, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Configuration/ChannelCertificateUiImpl.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700400590, "dur":1828, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Configuration/AutoConfig.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700397736, "dur":6163, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700404345, "dur":1304, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/CanvasScalerEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700406102, "dur":1131, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/Properties/AssemblyInfo.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700407233, "dur":973, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/EventSystem/PhysicsRaycasterEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700408206, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/EventSystem/Physics2DRaycasterEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700408868, "dur":967, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/EventSystem/EventTriggerEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1748871700403899, "dur":6684, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700410583, "dur":2627, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700413211, "dur":12860, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700426073, "dur":1115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700427194, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700427520, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700427612, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700427721, "dur":281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700428003, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700428160, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700428320, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700428541, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700428605, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700428837, "dur":307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700429144, "dur":952, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700430103, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700430354, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700430453, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700430554, "dur":36808, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700467362, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700467889, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700467994, "dur":1406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700469400, "dur":1210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700470616, "dur":258, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700470874, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700471000, "dur":247, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700471247, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700471361, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700471623, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700471921, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700472038, "dur":53, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700472095, "dur":2135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700474232, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700474312, "dur":824, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700475137, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700475211, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700475282, "dur":1367, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700476660, "dur":600, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700477262, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700477973, "dur":4910, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700482883, "dur":21980, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700504864, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700505059, "dur":10170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700515229, "dur":740, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700515996, "dur":21429, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700537426, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700537552, "dur":173, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748871700537730, "dur":750, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748871700538480, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700538599, "dur":5342, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871700543941, "dur":537515, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701081470, "dur":32266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1748871701113738, "dur":1064, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701114823, "dur":10026, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1748871701124850, "dur":2157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701127009, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1748871701127083, "dur":13632, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1748871701140716, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701141327, "dur":42998, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1748871701184326, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701184674, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701184881, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701184979, "dur":665, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll" }}
,{ "pid":12345, "tid":2, "ts":1748871701185644, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701185786, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll" }}
,{ "pid":12345, "tid":2, "ts":1748871701185877, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701186945, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701187161, "dur":1028, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701188195, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701188254, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1748871701188361, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701188758, "dur":415, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":2, "ts":1748871701189173, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701189262, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701189407, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/FMODUnityResonance.pdb" }}
,{ "pid":12345, "tid":2, "ts":1748871701189575, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701189666, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701189782, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701189876, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701190613, "dur":1001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748871701191627, "dur":4576533, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700034871, "dur":103833, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700138710, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700139099, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700139229, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700139477, "dur":1050, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700140528, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700140602, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700140704, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700140809, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700140884, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700140972, "dur":1976, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700142948, "dur":1713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700144674, "dur":309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700144984, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700145539, "dur":854, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700146393, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700146474, "dur":864, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700147339, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700147627, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700147800, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700148002, "dur":1066, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700149068, "dur":1735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700151133, "dur":5196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700156330, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700156423, "dur":1130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700157554, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700157761, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700157816, "dur":1683, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700159500, "dur":964, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160480, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160540, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160671, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160736, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160852, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700160926, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700161042, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700161176, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700161240, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700161362, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162109, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162249, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162500, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162570, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162720, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162802, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700162940, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163211, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163291, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163384, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163547, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163645, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163709, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163769, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700163860, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164162, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164269, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164408, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164463, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164561, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164685, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700164942, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700165136, "dur":1200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700166349, "dur":1814, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700168174, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700168333, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700168598, "dur":1899, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700170510, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700171154, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700171288, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700171554, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700172115, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700172638, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/SettingsEditor.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700173858, "dur":2114, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/FMODEventTrackEditor.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700176215, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/FindAndReplace.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700176851, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/FileReorganizer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700177364, "dur":1267, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/EventReferenceUpdater.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700179391, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/EditorEventRef.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700172346, "dur":7826, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700180172, "dur":932, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Security.Principal.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700182377, "dur":1447, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700180172, "dur":4043, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700185369, "dur":981, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700186351, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/Grpc.Net.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700187499, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/Google.Protobuf.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700188388, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Unity.ILPP.Runner.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700189266, "dur":3597, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Unity.CompilationPipeline.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700184216, "dur":8871, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700193699, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700194800, "dur":996, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700195957, "dur":2000, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700193087, "dur":5013, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700198564, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.ResponseCompression.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871700198101, "dur":1756, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700201449, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.visualstudio@8140e851d83e/Editor/Cli.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700199857, "dur":2270, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700203671, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.rendering.light-transport@307bc27a498f/Editor/ShaderTemplates.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700202127, "dur":2772, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700205407, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGUI/Shaders/ParticlesLitShader.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700207187, "dur":1062, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/UniversalStructs.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700208249, "dur":1503, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/UniversalStructFields.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700209753, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/UniversalProperties.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700204900, "dur":5577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700211985, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/Selection/RectSelector.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700212494, "dur":1588, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/Selection/PointRectSelector.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700210477, "dur":3605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700214082, "dur":958, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Importers/ShaderSubGraphImporterEditor.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700215041, "dur":1830, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Importers/ShaderSubGraphImporter.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700216872, "dur":7198, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Importers/ShaderGraphMetadata.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700224421, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Importers/ShaderGraphAssetPostProcessor.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700225195, "dur":916, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/PreviewTarget.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700226111, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Fullscreen/Includes/FullscreenShaderPass.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700226875, "dur":1186, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Fullscreen/FullscreenSubTarget.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700228062, "dur":1064, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Fullscreen/FullscreenShaderGUI.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700229126, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Fullscreen/FullscreenMetaData.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700214082, "dur":16605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700230767, "dur":1046, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Descriptors/BlockFieldDescriptor.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700232515, "dur":4506, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Data/ConditionalField.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700237022, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Controls.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700237733, "dur":1437, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Collections/StructCollection.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700239171, "dur":1468, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Collections/RenderStateCollection.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700230687, "dur":10203, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700241396, "dur":3144, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/ShaderInputPropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700244541, "dur":1183, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SampleVirtualTextureNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700245724, "dur":1294, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SampleTexture3DNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700247018, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SampleTexture2DNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700247833, "dur":4340, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SampleTexture2DArrayNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700252173, "dur":1017, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SamplerStateNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700253336, "dur":1340, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/ProceduralVirtualTextureNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700240890, "dur":14441, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700255332, "dur":1062, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700256394, "dur":1416, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700257811, "dur":1553, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700259365, "dur":1245, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700260610, "dur":758, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700261368, "dur":1784, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700263513, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/SampleDependencyImportSystem/SampleList.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700264280, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RenderGraph/RenderGraphViewer.SidePanel.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700265042, "dur":1390, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RenderGraph/RenderGraphViewer.PassTitleLabel.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700266433, "dur":3425, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RenderGraph/RenderGraphViewer.PanManipulator.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700270310, "dur":1437, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RenderGraph/RenderGraphViewer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700272122, "dur":1168, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RemoveComponentUtils.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700273291, "dur":2370, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RemoveAdditionalDataUtils.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700275661, "dur":1604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/RelativePropertiesDrawer.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700277635, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/PropertyFetcher.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700278901, "dur":984, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Properties/AdvancedProperties.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700263153, "dur":17207, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700280643, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/UniversalRenderPipelineCore.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700281247, "dur":1051, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/UniversalRendererRenderGraph.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700282299, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/UniversalRendererDebug.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700280360, "dur":3653, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700284137, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Decal/Entities/DecalDrawSystem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700284722, "dur":936, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Decal/Entities/DecalCreateDrawCallSystem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700286279, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Decal/DecalPreviewPass.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700284013, "dur":3569, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700288819, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Framework/Nesting/NestrerUnitWidget.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700287582, "dur":2870, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700291192, "dur":1283, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/GPUDriven/GPUResidentDrawerResources.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700292475, "dur":861, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/GPUDriven/GPUResidentDrawerDebug.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700290452, "dur":3269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700294227, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Multiply.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700295461, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Minimum.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700295995, "dur":962, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Maximum.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700293721, "dur":4775, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700298983, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Input/OnMouseInput.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700299675, "dur":1968, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Input/OnMouseExit.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700302235, "dur":2319, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Input/OnButtonInput.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700304884, "dur":1473, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/PointerEventUnit.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700306358, "dur":1586, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnToggleValueChanged.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700307944, "dur":2902, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnSubmit.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700310846, "dur":1720, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnSliderValueChanged.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700298496, "dur":14674, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700313871, "dur":815, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Connections/UnitRelation.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700314686, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Connections/UnitConnectionDebugData.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700315678, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Connections/InvalidConnection.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700316400, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Connections/ControlConnection.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700317372, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/Wizard.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700317984, "dur":983, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/WindowClose.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700313170, "dur":6752, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700319922, "dur":1761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700321683, "dur":3403, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700325423, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Context/GraphContextMenuItem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700326917, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Context/GraphContextExtension.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700329467, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Canvases/CanvasControlScheme.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700330145, "dur":1766, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Canvases/CanvasAttribute.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700332368, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/BoltProduct.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700325086, "dur":7916, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700333807, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/ColorUtils.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700335485, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Utilities/BatchRendererGroupGlobals.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700338021, "dur":994, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Textures/MSAASamples.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700339015, "dur":1189, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Textures/DepthBits.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700340205, "dur":4619, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Textures/BufferedRTHandleSystem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700333002, "dur":11822, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700344825, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerEnumHistory.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700345542, "dur":875, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerEnumField.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700347588, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/IVolumeDebugSettings.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700348174, "dur":1984, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/IDebugDisplaySettingsQuery.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700350158, "dur":3066, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/IDebugDisplaySettingsPanel.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700344825, "dur":9394, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700354280, "dur":870, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/TypeNameDetail.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700354219, "dur":2572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700356792, "dur":1124, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/UIInterfaces/UnityOnPointerEnterMessageListener.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700357917, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/UIInterfaces/UnityOnPointerDownMessageListener.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700359333, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/UIInterfaces/UnityOnEndDragMessageListener.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700359888, "dur":1159, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/UIInterfaces/UnityOnDropMessageListener.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700361316, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/UIInterfaces/UnityOnDeselectMessageListener.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700356792, "dur":5977, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700364018, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/EditorBinding/IInspectableAttribute.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700365144, "dur":1190, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Dependencies/FullSerializer/Reflection/fsTypeCache.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700362769, "dur":3565, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700366334, "dur":1046, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections/Unicode.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700366334, "dur":2400, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700368737, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework.performance@fb0dc592af8b/Runtime/ResourcesLoader.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700370807, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework.performance@fb0dc592af8b/Runtime/Measurements/MethodMeasurement.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700368735, "dur":3281, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700372996, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Plugins/DualShock/DualShockGamepad.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700373623, "dur":2753, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Plugins/Android/AndroidSupport.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700372016, "dur":4361, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700376705, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/ControlPicker/InputControlDropdownItem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700377338, "dur":2744, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/ControlPicker/IInputControlPickerLayout.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700380692, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/AssetEditor/ParameterListView.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700381442, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/AssetEditor/NameAndParameterListView.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700382257, "dur":3488, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/AssetEditor/InputBindingPropertiesView.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700385745, "dur":2364, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/AssetEditor/InputActionTreeViewItems.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700376377, "dur":12393, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700389613, "dur":1025, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Actions/IInputActionCollection.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700388770, "dur":3580, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700393029, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/ChangeTreeViewItem.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700392350, "dur":2032, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700395011, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UI/PlasticDialog.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700394383, "dur":3459, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700398087, "dur":1323, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/AssetsUtils/GetSelectedPaths.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700399411, "dur":1022, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/AssetsUtils/AssetsPath.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700400433, "dur":1795, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/AssetOverlays/DrawAssetOverlay.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700404355, "dur":1303, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/AssetMenu/AssetCopyPathOperation.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700397842, "dur":7816, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700405678, "dur":800, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700407024, "dur":826, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/TestJobDataHolder.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700409354, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/Tasks/Scene/SaveModifiedSceneTask.cs" }}
,{ "pid":12345, "tid":3, "ts":1748871700406478, "dur":3927, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700410406, "dur":187, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700410593, "dur":2787, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700413381, "dur":30431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700443873, "dur":23031, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700466904, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700467435, "dur":7369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700474833, "dur":3153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700478045, "dur":259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700478346, "dur":715, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700479119, "dur":3767, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700482887, "dur":30483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700513400, "dur":602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700514002, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700514209, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700514377, "dur":13226, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700527605, "dur":11273, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700538910, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700539308, "dur":1605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700540998, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700541216, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700541306, "dur":652, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700541982, "dur":206, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748871700543605, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748871700542197, "dur":1495, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871700543954, "dur":146, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871700545713, "dur":4315436, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871704868715, "dur":2077, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll" }}
,{ "pid":12345, "tid":3, "ts":1748871704866530, "dur":4675, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871704871680, "dur":419, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871704872103, "dur":63, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871704873189, "dur":541110, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748871705415418, "dur":2668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1748871705418668, "dur":255, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871705758429, "dur":358, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871705419145, "dur":339664, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1748871705762511, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb" }}
,{ "pid":12345, "tid":3, "ts":1748871705762641, "dur":671, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748871705763321, "dur":929, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb" }}
,{ "pid":12345, "tid":3, "ts":1748871705764250, "dur":3979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700035083, "dur":103651, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700138741, "dur":618, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700139461, "dur":789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700140251, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700140571, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700140686, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700140761, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700140904, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700141029, "dur":1951, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700142986, "dur":1797, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700144784, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700145051, "dur":1315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700146366, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700146532, "dur":1052, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700147651, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700147848, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700148741, "dur":714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700149455, "dur":3720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700153287, "dur":3589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700156876, "dur":849, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700157737, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700157806, "dur":1455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700159262, "dur":1181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700160455, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700160518, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700160682, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700160804, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700160867, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700160943, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700161102, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700161241, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700161339, "dur":205, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748871700161546, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162214, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162458, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162546, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162616, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162693, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162763, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162843, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700162904, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163047, "dur":209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163258, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163338, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163467, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163637, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163695, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163762, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700163839, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164131, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164258, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164377, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164461, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164536, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164662, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700164934, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700165121, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700165457, "dur":2670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700168138, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700168276, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700168554, "dur":1849, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700170419, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700171097, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700171253, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700171510, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700171586, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700173433, "dur":2345, "ph":"X", "name": "File",  "args": { "detail":"Assets/Kamgam/SkyCloudsURP/Runtime/Script/SkyCloudObserver.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700176345, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"Assets/Kamgam/SkyCloudsURP/Runtime/EditorScripts/VersionHelper.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700177223, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"Assets/Kamgam/SkyCloudsURP/Runtime/EditorScripts/Logger.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700178052, "dur":616, "ph":"X", "name": "File",  "args": { "detail":"Assets/Kamgam/SkyCloudsURP/Runtime/EditorScripts/Installer.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700179444, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/PPv2/PPv2Converter.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700172224, "dur":7888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700180112, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/platforms/html5/src/PlatformWebGL.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700181913, "dur":1787, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/Tests/TestFixture/InputTestFixture.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700180112, "dur":4078, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700185710, "dur":1089, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700187132, "dur":782, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700188712, "dur":3621, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700192333, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700184191, "dur":8780, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700193720, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700194817, "dur":1025, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Private.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700195843, "dur":2093, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Private.Uri.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700192972, "dur":5110, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700198598, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Options.ConfigurationExtensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700198082, "dur":1757, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700199839, "dur":1643, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700201483, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/TMP/PropertyDrawers/GlyphRectPropertyDrawer.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700203663, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.rider@7921be93db40/Rider/Editor/Util/RiderPathUtil.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700201483, "dur":3369, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700205374, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.cursor@2c0153a9bab1/Editor/Messaging/Messenger.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700207202, "dur":1451, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.cursor@2c0153a9bab1/Editor/Image.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700208653, "dur":1648, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.cursor@2c0153a9bab1/Editor/FileUtility.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700204852, "dur":5555, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700212318, "dur":1711, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Camera/UniversalRenderPipelineCameraUI.PhysicalCamera.Drawers.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700210407, "dur":3623, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700214102, "dur":1507, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/Converter/ParametricToFreeformLightUpgrader.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700215610, "dur":8272, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/Converter/BuiltInToURP2DMaterialUpgrader.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700224445, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Util/ValueUtilities.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700225282, "dur":965, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Util/MessageManager.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700226248, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Util/ListUtilities.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700226925, "dur":2071, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Util/IndexSet.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700229160, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Util/Documentation.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700214030, "dur":16582, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700230725, "dur":1139, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Processors/GraphCompilationResult.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700232146, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Processors/ActiveFields.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700232856, "dur":4601, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/OutputMetadata.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700237746, "dur":2464, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Enumerations/StructFieldOptions.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700230612, "dur":10289, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700241566, "dur":3776, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/AbstractMaterialNodePropertyDrawer.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700245343, "dur":1074, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/MasterPreviewView.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700246418, "dur":834, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/InspectorView.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700247252, "dur":2369, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/INodeModificationListener.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700249622, "dur":3193, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/EdgeConnectorListener.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700252816, "dur":1561, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/VectorControl.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700240901, "dur":14424, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700255326, "dur":1060, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700256386, "dur":1399, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700257785, "dur":1584, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700259369, "dur":1244, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700260613, "dur":763, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700261377, "dur":2109, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700264386, "dur":1851, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/MaterialUpgrader.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700266238, "dur":2694, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Material/MaterialHeaderScopeList.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700268933, "dur":2727, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Material/MaterialHeaderScopeItem.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700272016, "dur":1144, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Material/MaterialEditorExtension.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700273160, "dur":2380, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Material/DecalPreferences.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700275540, "dur":1695, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Material/AssetReimportUtils.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700277613, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/Stage.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700278883, "dur":999, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/EnvironmentLibrary.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700263487, "dur":16919, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700280641, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/StencilUsage.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700281245, "dur":1036, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/ShadowUtils.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700282282, "dur":804, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/ShadowCulling.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700280406, "dur":3645, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700284349, "dur":1304, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Data/UniversalRenderPipelineAssetPrefiltering.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700286277, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Data/PostProcessData.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700284051, "dur":3562, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700288811, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Framework/Control/SwitchOnIntegerDescriptor.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700287614, "dur":2871, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700291180, "dur":1061, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/ScriptMachine.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700292241, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/ScriptGraphAsset.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700292830, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Properties/AssemblyInfo.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700290485, "dur":3612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700294148, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector2/Vector2Normalize.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700295281, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector2/Vector2Modulo.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700295860, "dur":1069, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector2/Vector2Minimum.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700294097, "dur":4479, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700298704, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnScroll.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700299597, "dur":1317, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnPointerUp.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700300914, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnPointerExit.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700302207, "dur":2299, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnMove.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700304925, "dur":3000, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnDrag.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700307926, "dur":2894, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnDeselect.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700310821, "dur":1444, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnCancel.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700312274, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnButtonClick.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700298577, "dur":14751, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700314130, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/Sidebars/ISidebarPanelContent.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700315704, "dur":1085, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/GraphInspectorPanel.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700316789, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/GeneratePropertyProvidersWindow/GeneratePropertyProvidersPage.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700317705, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/EditorWindowWrapper.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700318489, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Windows/ConfigurationPanel/ConfigurationPanel.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700313346, "dur":6517, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700319864, "dur":1759, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700321624, "dur":3127, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700325817, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/ReorderableList/Internal/ReorderableListResources.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700326761, "dur":731, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/ReorderableList/GenericListAdaptor.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700329547, "dur":1088, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Decorators/MultiDecoratorProvider.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700330635, "dur":1361, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Debugging/GraphDebugDataProvider.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700324751, "dur":7430, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700332604, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/AssemblyInfo.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700333664, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/XR/XRPass.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700335699, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/XR/XRBuiltinShaderConstants.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700337890, "dur":817, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Volume/VolumeCollection.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700338708, "dur":1271, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Volume/Volume.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700332181, "dur":7798, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700339979, "dur":2636, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerValue.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700342615, "dur":2913, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerUIntField.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700345529, "dur":875, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerToggleHistory.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700347343, "dur":2386, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerObjectList.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700349729, "dur":3474, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerObject.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700339979, "dur":14174, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700354355, "dur":1006, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Serialization/SerializeAttribute.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700354153, "dur":2617, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700356771, "dur":1081, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Pooling/ManualPool.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700357980, "dur":905, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Pooling/IPoolable.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700359273, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Pooling/GenericPool.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700359860, "dur":1604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Pooling/DictionaryPool.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700361465, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Pooling/ArrayPool.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700356771, "dur":6143, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700363958, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Dependencies/FullSerializer/Converters/Unity/LayerMask_DirectConverter.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700365168, "dur":2177, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Dependencies/FullSerializer/Converters/Unity/AnimationCurve_DirectConverter.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700362919, "dur":4481, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700368802, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/Utilities/WeightUtility.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700367401, "dur":2221, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700370768, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Utilities/Observables/Observable.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700369623, "dur":2448, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700372815, "dur":615, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/Views/PropertiesView.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700373431, "dur":2600, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/Views/NameAndParametersListView.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700376031, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/Views/MatchingControlPaths.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700372071, "dur":4816, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700376888, "dur":3024, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Mouse.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700380918, "dur":891, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/IEventPreProcessor.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700381809, "dur":3553, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/IEventMerger.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700385363, "dur":1864, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/ICustomDeviceReset.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700387227, "dur":1014, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Haptics/IHaptics.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700376888, "dur":11986, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700388978, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/Noise/cellular2x2.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700389557, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/Noise/cellular2D.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700388874, "dur":3588, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700393010, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/DirectoryConflicts/AddMoveMenu.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700392463, "dur":2071, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700394987, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Tool/IsExeVersion.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700394534, "dur":3535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700398070, "dur":1335, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/Intrinsics/v64.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700399405, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/Intrinsics/v256.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700400059, "dur":1898, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/Intrinsics/v128.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700404353, "dur":1302, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/Editor/BurstCompileTarget.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700398069, "dur":8038, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700407045, "dur":807, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRunner/Utils/CachingTestListProvider.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700409356, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs" }}
,{ "pid":12345, "tid":4, "ts":1748871700406127, "dur":4172, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700410299, "dur":267, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700410567, "dur":1667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700412235, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700412310, "dur":1250, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700413561, "dur":48899, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700462464, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700462994, "dur":125, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700463126, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700463369, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700463474, "dur":10515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700473989, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700474236, "dur":749, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700474986, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll" }}
,{ "pid":12345, "tid":4, "ts":1748871700475176, "dur":7702, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700482880, "dur":18575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700501474, "dur":1942, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700503417, "dur":1147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700504581, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700504642, "dur":10760, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700515402, "dur":4580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700520031, "dur":12634, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700533089, "dur":869, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700533984, "dur":1175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700535161, "dur":761, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700536112, "dur":603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700536751, "dur":1163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700537915, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700538028, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748871700538123, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700538244, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748871700538691, "dur":5252, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871700543943, "dur":537572, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701081519, "dur":49505, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701131026, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701131491, "dur":6659, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701138151, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701138382, "dur":4680, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701143062, "dur":1525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701144599, "dur":6945, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701151544, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701151637, "dur":4354, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701155991, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701156371, "dur":6554, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701162929, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701163458, "dur":4060, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701167519, "dur":2068, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701169599, "dur":5644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701175244, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701176109, "dur":2995, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701179105, "dur":901, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748871701180027, "dur":11560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1748871701191620, "dur":4576516, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700035124, "dur":103636, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700138768, "dur":268, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700139041, "dur":136, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700139178, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700139352, "dur":1171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700140523, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700140592, "dur":273, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700140865, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700140932, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700141043, "dur":1973, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700143021, "dur":1945, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700144966, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700145319, "dur":1108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700146428, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700146589, "dur":1007, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700147596, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700147658, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700147835, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700148665, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700149169, "dur":2883, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700152067, "dur":4282, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700156350, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700156449, "dur":1211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700157660, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700157765, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700157879, "dur":1832, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700159735, "dur":772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700160565, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700160696, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700160871, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700160951, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700161132, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700161264, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700161429, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162173, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162318, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162522, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162593, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162675, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162730, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162815, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162881, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700162946, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163222, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163297, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163392, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163591, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163654, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163718, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163786, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700163903, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164144, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164297, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164359, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164421, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164473, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164578, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164675, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164748, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700164979, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700165153, "dur":1207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700166363, "dur":588, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp" }}
,{ "pid":12345, "tid":5, "ts":1748871700166952, "dur":1235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700168189, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700168395, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700168651, "dur":1976, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700170633, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700171190, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700171339, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700171566, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/BoltIntegration.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/BankRefDrawer.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":1926, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/StudioParameterTrigger.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/StudioGlobalParameterTrigger.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":1170, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/StudioBankLoader.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/PlatformDefault.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Platform.cs" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":8348, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":1622, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":3516, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":****************, "dur":1162, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700187129, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700188584, "dur":1894, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700190479, "dur":2408, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700184226, "dur":8886, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700193712, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700194810, "dur":1000, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700195810, "dur":2105, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700193112, "dur":5005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700198584, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Localization.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871700198117, "dur":1839, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700201467, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/TMP/TMP_SpriteAssetImporter.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700199956, "dur":2213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700203652, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/QuickstartPackageHandling.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700202169, "dur":2714, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700205362, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/UniversalRenderPipelineAsset/UniversalRenderPipelineAssetUI.Drawers.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700207212, "dur":1630, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderStripTool.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700208843, "dur":1465, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderScriptableStripper.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700204884, "dur":5529, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700212335, "dur":1716, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/AssetPostProcessors/FBXMaterialDescriptionPreprocessor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700210413, "dur":3639, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700214052, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/ShaderGraphToolbarExtension.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700214596, "dur":1325, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/ShaderGraphShortcuts.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700215921, "dur":8024, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/ShaderGraphProjectSettings.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700224402, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/ShaderGraphDataExtension.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700225613, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Serialization/MultiJsonInternal.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700226570, "dur":1210, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Serialization/MultiJsonEntry.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700227781, "dur":1234, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Serialization/MultiJson.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700214052, "dur":16562, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700230788, "dur":1180, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Enumerations/KeywordShaderStage.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700232202, "dur":1111, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Enumerations/DisableBatching.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700233314, "dur":4156, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Enumerations/Cull.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700237748, "dur":2464, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Descriptors/RenderStateDescriptor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700230614, "dur":10239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700241579, "dur":3779, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Interfaces/ISGControlledElement.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700245359, "dur":1088, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Interfaces/IResizable.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700246447, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Interfaces/IRectInterface.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700247259, "dur":2438, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/WindowDockingLayout.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700249698, "dur":3145, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/TabbedView/TabButton.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700252843, "dur":1742, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/TabbedView/TabbedView.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700240854, "dur":14440, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700255294, "dur":1067, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700256362, "dur":1398, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700257760, "dur":1559, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700259319, "dur":1230, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700260550, "dur":778, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700261328, "dur":1419, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700264273, "dur":520, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ShaderStripping/ShaderPreprocessor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700264794, "dur":1333, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ShaderStripping/ShaderExtensions.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700266127, "dur":2399, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ShaderStripping/IVariantStripper.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700268813, "dur":2834, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ShaderGenerator/ShaderTypeGeneration.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700272007, "dur":1031, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ShaderGenerator/CSharpToHLSL.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700273039, "dur":2407, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Settings/SettingsProviderGUIScope.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700275446, "dur":1450, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Settings/RenderPipelineResourcesEditor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700276896, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Settings/RenderPipelineGlobalSettingsUI.Drawers.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700277546, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Settings/RenderPipelineGlobalSettingsProvider.deprecated.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700278862, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Settings/PropertyDrawers/RenderPipelineGraphicsSettingsContainerPropertyDrawer.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700262747, "dur":17391, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700280636, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/BuildTargetExtensions.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700281238, "dur":1029, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/BuildProcessors/SettingsStrippers/GPUResidentDrawerResourcesStripper.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700282268, "dur":808, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/BuildProcessors/CorePreprocessBuild.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700280138, "dur":3818, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700284164, "dur":1450, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Overrides/ColorAdjustments.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700286320, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/NoAllocUtils.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700283956, "dur":3553, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700288805, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_0_3.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700287509, "dur":2892, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700291238, "dur":1354, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.rendering.light-transport@307bc27a498f/Runtime/UnifiedRayTracing/Common/Utilities/Utils.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700292592, "dur":859, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.rendering.light-transport@307bc27a498f/Runtime/UnifiedRayTracing/Common/Utilities/PersistentGPUArray.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700290401, "dur":3209, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700294134, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/GetVariable.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700295194, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Time/WaitUnit.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700296075, "dur":906, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Time/WaitForNextFrameUnit.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700293611, "dur":4834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700298667, "dur":847, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics/OnParticleCollision.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700299703, "dur":1984, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Physics/OnControllerColliderHit.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700302458, "dur":2216, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Navigation/OnDestinationReached.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700304902, "dur":1978, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Lifecycle/OnDisable.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700306880, "dur":2647, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Lifecycle/OnDestroy.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700309528, "dur":2439, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Lifecycle/LateUpdate.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700311968, "dur":876, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Lifecycle/FixedUpdate.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700298446, "dur":14500, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700314123, "dur":1049, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Dependencies/NCalc/TernaryExpression.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700315717, "dur":1150, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Dependencies/NCalc/LogicalExpression.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700317846, "dur":994, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Dependencies/NCalc/FunctionArgs.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700312946, "dur":6848, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700319794, "dur":1739, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700321533, "dur":3169, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700325421, "dur":782, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/EditorProvider.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700326779, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Graph/LudiqGraphsEditorUtility.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700329433, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Descriptors/DescriptorProvider.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700330138, "dur":1726, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Descriptors/DescriptorAttribute.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700324703, "dur":7407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700333776, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_CharacterInfo.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700335472, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMPro_EventManager.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700337967, "dur":1037, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/FontFeatureCommon.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700332111, "dur":6893, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700339004, "dur":1007, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/SphericalHarmonics.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700340011, "dur":4387, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ShaderVariablesProbeVolumes.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700344399, "dur":1220, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumeStreamableAsset.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700345620, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumesOptions.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700347167, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumeGIContributor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700347718, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumeConstantRuntimeResources.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700348293, "dur":2913, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumeBakingSet.Editor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700351207, "dur":2102, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Lighting/ProbeVolume/ProbeVolumeBakingSet.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700339004, "dur":15086, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700354144, "dur":1203, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Variables/ObjectVariables.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700356760, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Utilities/HashUtility.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700354091, "dur":3417, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700357933, "dur":891, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnTriggerExit2DMListener.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700359009, "dur":799, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnTriggerEnter2DMListener.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700359809, "dur":1065, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnTransformParentChangedMListener.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700361388, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnMouseUpMessageListener.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700357509, "dur":5268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700365152, "dur":2175, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Dependencies/FullSerializer/fsExceptions.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700362777, "dur":4550, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700367327, "dur":1437, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700368765, "dur":837, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework.performance@fb0dc592af8b/Runtime/Data/Player.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700370799, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/Preset.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700368765, "dur":3275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700372930, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Events/TextEvent.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700373536, "dur":2762, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Events/StateEvent.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700372040, "dur":4662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700376703, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Analytics/VirtualMouseInputEditorAnalytic.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700377336, "dur":2742, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Analytics/PlayerInputManagerEditorAnalytic.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700380676, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Touchscreen.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700381258, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Sensor.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700381813, "dur":3843, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Remote/RemoteInputPlayerConnection.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700385656, "dur":2419, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Remote/InputRemoting.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700376702, "dur":12123, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700389615, "dur":955, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/uint2.gen.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700388825, "dur":3584, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700392409, "dur":1982, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700394981, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UI/DrawStaticElement.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700396235, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UI/DrawActionHelpBox.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700394391, "dur":3557, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700398054, "dur":942, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/ApplicationDataPath.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700398996, "dur":948, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/SharedStatic.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700399944, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/SafeStringArrayHelper.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700400548, "dur":1863, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/NoAliasAttribute.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700404308, "dur":1339, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/Intrinsics/x86/Avx2.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700397948, "dur":7774, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700406886, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/UGUI/EventSystem/EventSystem.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700405722, "dur":3179, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700409328, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/GUI/TestAssets/ICustomScriptAssemblyMappingFinder.cs" }}
,{ "pid":12345, "tid":5, "ts":1748871700408942, "dur":1436, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700410378, "dur":52, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700410556, "dur":758, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700411316, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700411450, "dur":2612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700414062, "dur":3790, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700417853, "dur":117, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700418001, "dur":3453, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700421473, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700421598, "dur":4438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700426082, "dur":8739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700434822, "dur":3178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700438022, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700438669, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700438762, "dur":385, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700439151, "dur":340, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700439491, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700439589, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700439664, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700440120, "dur":746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700440866, "dur":797, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700441677, "dur":1270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700442964, "dur":336, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700443305, "dur":11660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700454967, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700455054, "dur":730, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700455786, "dur":11625, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700467412, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700468079, "dur":428, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700468508, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700469113, "dur":3108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700472222, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700472387, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700472565, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700472796, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700473097, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700473163, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700473219, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700473475, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700473653, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700473825, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700473904, "dur":293, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700474197, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb" }}
,{ "pid":12345, "tid":5, "ts":1748871700474277, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700474881, "dur":106, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700474987, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb" }}
,{ "pid":12345, "tid":5, "ts":1748871700475180, "dur":7700, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700482881, "dur":19863, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700502744, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700503072, "dur":2490, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700505563, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700506244, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700506493, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700506574, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700506625, "dur":571, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700507197, "dur":4583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700511781, "dur":1507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700513326, "dur":1076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700514421, "dur":4590, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700519013, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700519699, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700519871, "dur":17585, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700537457, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748871700537559, "dur":1176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748871700538746, "dur":5205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871700543952, "dur":537589, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701081545, "dur":4936, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701086483, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701086635, "dur":7078, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701093715, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701093799, "dur":4744, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701098548, "dur":2055, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701100616, "dur":6955, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701107625, "dur":981, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701108619, "dur":2362, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701110982, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701111175, "dur":29260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701140435, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701140515, "dur":13602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701154118, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871701156011, "dur":35254, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1748871701191307, "dur":4571216, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871705762560, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871705762543, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871705762651, "dur":697, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748871705763361, "dur":861, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":5, "ts":1748871705764233, "dur":3867, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700035139, "dur":103649, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700138792, "dur":241, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700139058, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700139224, "dur":203, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700139428, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700139496, "dur":982, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700140503, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700140611, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700140697, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700140867, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700140979, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700141040, "dur":3639, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700144679, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700144957, "dur":290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700145247, "dur":966, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700146221, "dur":335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700146557, "dur":868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700147433, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700147641, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700147724, "dur":953, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700148678, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700148930, "dur":3305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700152236, "dur":4058, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700156312, "dur":233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700156545, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700157089, "dur":679, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700157769, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700157844, "dur":1938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700159782, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700160564, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700160703, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700160896, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700160971, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700161151, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700161228, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700161312, "dur":251, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700161565, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162235, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162482, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162605, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162689, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162746, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162891, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700162987, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163236, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163332, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163405, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163594, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163655, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163722, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700163795, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164042, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164148, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164301, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164355, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164418, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164470, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164565, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164658, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700164900, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700165051, "dur":365, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700165430, "dur":2672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700168115, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700168267, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700168542, "dur":1839, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700170384, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748871700170445, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700171112, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700171266, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700171528, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700171663, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700173444, "dur":2415, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/PPv2/EffectConverters/GrainConverter.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700176179, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/PPv2/EffectConverters/ColorGradingConverter.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700177288, "dur":1327, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/PPv2/EffectConverters/BloomConverter.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700179381, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/Editor/StudioEventEmitterGizmoDrawer.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700172240, "dur":7882, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700180122, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700182073, "dur":1731, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700180122, "dur":4085, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700185765, "dur":1146, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700187238, "dur":863, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700188308, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700188911, "dur":3924, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700184207, "dur":8864, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700194791, "dur":980, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700195914, "dur":2041, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Net.Mail.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700193071, "dur":5028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700198563, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Caching.Abstractions.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700198099, "dur":1756, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700201447, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.visualstudio@8140e851d83e/Editor/ProjectGeneration/ProjectGeneration.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700199855, "dur":2265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700203684, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.rider@7921be93db40/Rider/Editor/ProjectGeneration/ProjectPart.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700202120, "dur":2748, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700205347, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ai.navigation@c74b2724ed42/Editor/Updater/NavigationUpdaterEditor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700207200, "dur":1344, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/VFXGraph/VFXShaderGraphGUI.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700208544, "dur":1747, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/VFXGraph/VFXDecalURPOutput.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700204868, "dur":5531, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700212316, "dur":1111, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/RunItemContext.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700210400, "dur":3028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700213429, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/Shadows/ShadowCaster2DEditor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700214099, "dur":1409, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/Shadows/CastingSourceDropDown.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700215508, "dur":8248, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShaderGraph/Targets/UniversalSpriteUnlitSubTarget.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700224447, "dur":1417, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShaderGraph/Nodes/LightTextureNode.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700225865, "dur":793, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteUnlitShaderGraph.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700226659, "dur":1139, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteLitShaderGraph.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700227799, "dur":1222, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteCustomLitShaderGraph.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700231243, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/Converter/URP2DConverterUtility.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700213428, "dur":18554, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700232216, "dur":4198, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Collections/AdditionalCommandCollection.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700236416, "dur":1102, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Attributes/GenerateBlocksAttribute.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700237781, "dur":2453, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Views/Slots/TextureSlotControlView.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700231982, "dur":9375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700241357, "dur":1285, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/GradientControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700242643, "dur":2862, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/EnumConversionControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700245505, "dur":1199, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/EnumControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700246704, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/DielectricSpecularControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700247364, "dur":4224, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/DefaultControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700251588, "dur":1393, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/CubemapControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700252981, "dur":1664, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Controls/ColorControl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700241357, "dur":14151, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700255509, "dur":1042, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700256552, "dur":1411, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700257964, "dur":1549, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700259513, "dur":1254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700260767, "dur":722, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700263521, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.State/Plugin/Changelogs/Changelog_1_1_1.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700261489, "dur":2798, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700264288, "dur":974, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/Context.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700265262, "dur":1498, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/Compositor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700266760, "dur":4568, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/ComparisonGizmoState.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700272165, "dur":1298, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/LookDev/CameraController.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700273463, "dur":2560, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/Shadow/ShadowCascadeGUI.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700276023, "dur":1327, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolumeMenuItems.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700277351, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/SerializedProbeVolume.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700278521, "dur":809, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeVolumeUI.Drawer.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700279330, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeVolumesOptionsEditor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700264288, "dur":16264, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700280618, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/RenderTargetBufferSystem.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700281680, "dur":1383, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeShaders.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700280552, "dur":3799, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700284351, "dur":1300, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.State/AnyState.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700286276, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Units/UnitEditor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700284351, "dur":3596, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700288799, "dur":803, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Events/GlobalMessageListenerEditor.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700287947, "dur":2749, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700291174, "dur":977, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/INesterUnit.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700292152, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/IDefaultValue.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700292723, "dur":808, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/UnifiedVariableUnit.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700294146, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/SetVariableUnit.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700295201, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/SetObjectVariable.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700290696, "dur":5154, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700296205, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Minimum.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700298677, "dur":903, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Add.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700299580, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Absolute.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700300293, "dur":1413, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Logic/Or.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700295850, "dur":6005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700302205, "dur":2321, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/SwitchUnit.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700304983, "dur":2931, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/SelectOnString.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700307914, "dur":2886, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/SelectOnInteger.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700310801, "dur":1269, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/SelectOnFlow.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700312071, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/SelectOnEnum.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700314124, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Control/For.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700301856, "dur":13028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700315696, "dur":975, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Variables/InspectorVariableFieldAttributeInspector.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700316672, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Variables/EditorVariablesUtility.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700317670, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Utilities/Warning.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700318334, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Utilities/VSUsageUtility.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700314884, "dur":5375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700320259, "dur":2106, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700324841, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/Primitives/DecimalInspector.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700325415, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/Primitives/CharInspector.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700322366, "dur":4229, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700326909, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_UpdateRegistery.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700329431, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_SubMeshUI.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700330136, "dur":1723, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_SubMesh.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700332289, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_SpriteGlyph.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700326595, "dur":7081, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700333676, "dur":779, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphUtilsBlit.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700335736, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphResourceBuffer.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700338087, "dur":1129, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphGlobalSettings.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700339217, "dur":1311, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphDefaultResources.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700340529, "dur":4716, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphCompilationCache.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700345245, "dur":875, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphBuilders.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700333676, "dur":13011, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700347183, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Common/IVirtualTexturingEnabledRenderPipeline.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700348078, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Common/IAdditionalData.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700348837, "dur":4209, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Common/GlobalDynamicResolutionSettings.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700354302, "dur":1039, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/CommandBuffers/UnsafeCommandBuffer.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700346687, "dur":8767, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700357114, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/InstanceFunctionInvoker_3.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700357927, "dur":858, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/InstanceFunctionInvoker_2.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700355455, "dur":3888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700359343, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnCollisionExit2DMessageListener.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700360050, "dur":1004, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnCollisionEnterMessageListener.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700361386, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Listeners/MonoBehaviour/UnityOnBecameVisibleMessageListener.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700359343, "dur":3691, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700364257, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Dependencies/FullSerializer/Converters/fsDictionaryConverter.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700365143, "dur":1029, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Connections/InvalidConnectionException.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700366173, "dur":1206, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Connections/IConnectionCollection.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700363034, "dur":4509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700368711, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/TimelineAsset_CreateRemove.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700367543, "dur":2461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700370786, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Utilities/ArrayHelpers.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700370004, "dur":2185, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700372887, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/StateContainer.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700373452, "dur":2695, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/SerializedInputBinding.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700376626, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/SerializedInputAction.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700377329, "dur":2667, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/UITKAssetEditor/ReactiveProperty.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700372189, "dur":7994, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700380934, "dur":877, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Commands/QueryEnabledStateCommand.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700381811, "dur":3751, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Commands/QueryEditorWindowCoordinatesCommand.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700385562, "dur":1707, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Commands/QueryDimensionsCommand.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700387269, "dur":975, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Devices/Commands/QueryCanRunInBackground.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700380194, "dur":8903, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700389564, "dur":898, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/half4.gen.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700389097, "dur":3443, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700392540, "dur":2068, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700397981, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Hub/Operations/OperationParams.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700398716, "dur":1023, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Hub/Operations/DownloadRepository.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700394609, "dur":5130, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700399739, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/CompilerServices/SPMD.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700400479, "dur":1787, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.burst@7a907cf5a459/Runtime/CompilerServices/SkipLocalsInitAttribute.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700404348, "dur":1303, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ai.navigation@c74b2724ed42/Runtime/NavMeshSurface.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700406103, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ai.navigation@c74b2724ed42/Runtime/NavMeshLink.deprecated.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700399739, "dur":6900, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700407048, "dur":805, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/Tasks/PostbuildCleanupTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700409359, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/Tasks/Platform/PlatformSpecificCleanupTask.cs" }}
,{ "pid":12345, "tid":6, "ts":1748871700406639, "dur":3933, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700410572, "dur":2418, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700412992, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700413214, "dur":42244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748871700455459, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700455927, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700456021, "dur":749, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700456771, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700457256, "dur":3699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700460956, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700461076, "dur":745, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700461823, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700461950, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700462155, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700462306, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700462533, "dur":2204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700464738, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700465709, "dur":149, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700465860, "dur":8130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748871700473991, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700474104, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700474199, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll" }}
,{ "pid":12345, "tid":6, "ts":1748871700474281, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700474888, "dur":100, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700474988, "dur":5891, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700480880, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700481010, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700481075, "dur":33607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748871700514682, "dur":421, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700515137, "dur":863, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700516000, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700516499, "dur":12958, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748871700529457, "dur":774, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700530242, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700530357, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748871700530482, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748871700530816, "dur":6743, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700537560, "dur":6374, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871700543938, "dur":537441, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701081402, "dur":34064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701115467, "dur":373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701115862, "dur":5659, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701121522, "dur":984, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701122520, "dur":15458, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701137979, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701138089, "dur":8516, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701146606, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701147031, "dur":5912, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701152944, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701153043, "dur":4314, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701157358, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701157784, "dur":8594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701166381, "dur":1396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701167802, "dur":13754, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701181556, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701181653, "dur":1816, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1748871701183469, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701183703, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701184063, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701184681, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701184803, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701184938, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701185011, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701185695, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701185765, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871701185872, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701186880, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701187015, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701187091, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701187217, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701187451, "dur":772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871701188223, "dur":405, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701188637, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701188708, "dur":421, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871701189130, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701189222, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871701189296, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701189644, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701189735, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701189845, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701189914, "dur":1385, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871701191316, "dur":4576152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748871705767483, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871705767578, "dur":300, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1748871705767880, "dur":216, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700035149, "dur":103651, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700138805, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700139046, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700139201, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700139448, "dur":1076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700140525, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700140596, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700140702, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700140798, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700140868, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700140952, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700141046, "dur":1978, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700143028, "dur":1943, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700144971, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700145305, "dur":1120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700146425, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700146582, "dur":1010, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700147666, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700147839, "dur":870, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700148718, "dur":575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700149293, "dur":3326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700152720, "dur":3636, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700156356, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700156561, "dur":1126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700157687, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700157783, "dur":232, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700158015, "dur":2275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160302, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700160517, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160577, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160654, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160714, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160835, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160891, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700160959, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700161147, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700161225, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700161287, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700161550, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162218, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162460, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162557, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162702, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162787, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700162916, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163146, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163289, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163343, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163444, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163612, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163671, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163742, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700163829, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164105, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164231, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164366, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164478, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164629, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164775, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700164998, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700165198, "dur":2841, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700168054, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700168256, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700168436, "dur":1914, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700170366, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700171082, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700171246, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700171506, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700171575, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700173431, "dur":1606, "ph":"X", "name": "File",  "args": { "detail":"Assets/Scripts/RainDropAudio.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700175038, "dur":1132, "ph":"X", "name": "File",  "args": { "detail":"Assets/Scripts/RainDirector.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700176393, "dur":889, "ph":"X", "name": "File",  "args": { "detail":"Assets/Scripts/ParticleSnakeRiverController.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700177282, "dur":1298, "ph":"X", "name": "File",  "args": { "detail":"Assets/Scripts/ParticleSnakeController.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700179331, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"Assets/Scripts/EnvironmentManager.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700172220, "dur":7894, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700180115, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/DocCodeSamples.Tests/GamepadExample.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700181928, "dur":1861, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700180115, "dur":4082, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700185721, "dur":1159, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700187205, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700188608, "dur":3505, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700192114, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700184197, "dur":8771, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700193718, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Security.Claims.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700194815, "dur":1051, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700195866, "dur":2083, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700192969, "dur":5124, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700198558, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.FileProviders.Embedded.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871700198093, "dur":1756, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700199850, "dur":1523, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700201465, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/TMP/TMP_BaseShaderGUI.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700203650, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/TMP/PropertyDrawers/TMP_SpriteCharacterPropertyDrawer.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700201374, "dur":3119, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700205360, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.cursor@2c0153a9bab1/Editor/SolutionProperties.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700207221, "dur":1678, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ide.cursor@2c0153a9bab1/Editor/ProjectGeneration/ProjectGeneration.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700204493, "dur":4406, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700208900, "dur":1415, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ScriptableRendererFeatureProvider.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700208900, "dur":3022, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700212342, "dur":1718, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/PathComponentEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700214060, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePathInspector.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700214668, "dur":1436, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePath.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700216105, "dur":7859, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditablePath/Snapping.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700224404, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditablePath/ISnapping.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700225550, "dur":1002, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditablePath/EditablePathController.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700226552, "dur":1236, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditablePath/EditablePath.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700227789, "dur":1229, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditablePath/ControlPoint.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700211922, "dur":17998, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700230870, "dur":1095, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/BuiltIn/Editor/AssetVersion.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700232200, "dur":1002, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/TargetResources/Fields.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700233203, "dur":4246, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/TargetResources/FieldDependencies.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700237745, "dur":2459, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Processors/ShaderSpliceUtil.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700229920, "dur":10792, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700241404, "dur":3395, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/ViewModels/InspectorViewModel.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700244799, "dur":1413, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/ViewModels/BlackboardViewModel.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700246212, "dur":1002, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/ViewModels/BlackboardCategoryViewModel.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700247214, "dur":2109, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/SearchWindowProvider.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700249323, "dur":3335, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/SearchWindowAdapter.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700252658, "dur":1051, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/PreviewManager.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700253710, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/MaterialGraphPreviewGenerator.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700240713, "dur":14390, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700255104, "dur":1125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700256229, "dur":1213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700257443, "dur":1791, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700259235, "dur":1182, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700260417, "dur":872, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700261289, "dur":1284, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700263537, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.searcher@1e17ce91558d/Editor/Searcher/SearcherHighlighter.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700264285, "dur":849, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.searcher@1e17ce91558d/Editor/Searcher/SearcherDatabase.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700265187, "dur":1384, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.searcher@1e17ce91558d/Editor/Searcher/SearcherControl.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700266572, "dur":3778, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.searcher@1e17ce91558d/Editor/Searcher/SearcherAdapter.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700270350, "dur":1414, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.searcher@1e17ce91558d/Editor/Searcher/Searcher.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700272133, "dur":1319, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumesPreferences.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700273453, "dur":2456, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeProfileUtils.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700275909, "dur":1384, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeProfileFactory.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700277662, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeParameterDrawer.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700278467, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeGizmoDrawer.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700279298, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Volume/VolumeEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700262574, "dur":17407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700281553, "dur":1351, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/ICoreRenderPipelinePreferencesProvider.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700279982, "dur":3813, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700284145, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Passes/CopyColorPass.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700284909, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Passes/ColorGradingLutPass.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700286297, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Overrides/WhiteBalance.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700283796, "dur":3676, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700288803, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_7.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700287473, "dur":2882, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700291198, "dur":1353, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.rendering.light-transport@307bc27a498f/Runtime/UnifiedRayTracing/IRayTracingShader.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700292552, "dur":851, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.rendering.light-transport@307bc27a498f/Runtime/UnifiedRayTracing/IRayTracingBackend.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700290356, "dur":3048, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700294366, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/IsSceneVariableDefined.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700295121, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/IsObjectVariableDefined.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700296048, "dur":923, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/ISceneVariableUnit.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700293405, "dur":4954, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700298649, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Logic/LessOrEqual.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700299695, "dur":1972, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Logic/GreaterOrEqual.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700302261, "dur":2401, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Logic/Comparison.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700304895, "dur":1496, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Graph/SetGraph.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700306391, "dur":1563, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Graph/ScriptGraphContainerType.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700307954, "dur":3250, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Graph/HasScriptGraph.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700311205, "dur":1469, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Graph/HasGraph.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700298360, "dur":14315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700314102, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Collections/Lists/ListContainsItem.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700314684, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Collections/Lists/InsertListItem.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700315686, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Collections/LastItem.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700316579, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Collections/FirstItem.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700318105, "dur":1000, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Collections/Dictionaries/MergeDictionaries.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700312724, "dur":6818, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700319544, "dur":1912, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700321456, "dur":3139, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700325412, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/MetadataListAdaptor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700326771, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/MacroEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700329416, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/ImplementationInspector.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700330102, "dur":1703, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Inspection/GraphNestEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700324596, "dur":7210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700332275, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_Settings.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700333921, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_PackageResourceImporter.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700335601, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_LineInfo.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700337891, "dur":955, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Runtime/TMP/TMP_FontAsset.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700331807, "dur":7039, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700338863, "dur":865, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraph.Compiler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700339729, "dur":2540, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/IRenderGraphRecorder.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700342270, "dur":3245, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/IRenderGraphEnabledRenderPipeline.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700345516, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/IRenderGraphBuilder.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700347298, "dur":830, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/Compiler/FixedAttachmentArray.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700348128, "dur":2020, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/Compiler/CompilerContextData.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700350149, "dur":3064, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/PostProcessing/LensFlareOcclusionPermutation.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700338863, "dur":15197, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700354440, "dur":933, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/CommandBuffers/CommandBufferHelpers.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700354061, "dur":2622, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700356778, "dur":1121, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/NumericNegationHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700357899, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/MultiplicationHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700359318, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/LessThanOrEqualHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700359873, "dur":1151, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/LessThanHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700361509, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/InequalityHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700356683, "dur":5971, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700363227, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Events/FrameDelayedCallback.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700363968, "dur":845, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Events/EventBus.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700362655, "dur":2431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700365130, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Collections/DebugDictionary.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700365643, "dur":1721, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Collections/AotList.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700365086, "dur":3523, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700368698, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/Events/IMarker.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700370780, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/Audio/AudioPlayableAsset.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700368609, "dur":3283, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700371892, "dur":904, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700373119, "dur":2579, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/PropertyDrawers/GamepadButtonPropertyDrawer.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700375698, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/ProjectWideActions/ProjectWideActionsBuildProvider.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700376783, "dur":2953, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/TreeViewHelpers.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700380711, "dur":1030, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/MultiLevelDataSource.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700381741, "dur":2556, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/CallbackDataSource.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700384298, "dur":2522, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownWindow.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700386821, "dur":1394, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownState.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700372796, "dur":15659, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700388964, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/InputControlLayoutAttribute.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700389712, "dur":1002, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/InputControlExtensions.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700388456, "dur":3707, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700392164, "dur":2100, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700395021, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/UVCPackageVersion.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700394265, "dur":3442, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700397974, "dur":680, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Developer/UpdateProgress.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700398654, "dur":863, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Developer/ShelvedChangesNotification.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700399517, "dur":938, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Developer/ProgressOperationHandler.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700400455, "dur":1786, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Developer/IncomingChangesNotification.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700397708, "dur":6127, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700403933, "dur":1025, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/RawImageEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700404958, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/PropertyDrawers/SpriteStateDrawer.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700406850, "dur":1020, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/MenuOptions.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700407870, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/MaskEditor.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700409327, "dur":863, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/InterceptedEventsPreview.cs" }}
,{ "pid":12345, "tid":7, "ts":1748871700403835, "dur":6579, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700410559, "dur":911, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700411471, "dur":911, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700413564, "dur":65753, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748871700479320, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700479432, "dur":935, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_62EC740DD3AB3A8D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700480377, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700480518, "dur":1431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748871700482045, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700482522, "dur":17470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748871700499992, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700500765, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700500825, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700500933, "dur":22029, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748871700522963, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700524570, "dur":1493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700526064, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700526221, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700526561, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748871700526713, "dur":629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748871700527343, "dur":1504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700528864, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700529496, "dur":8061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700537557, "dur":5896, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871700543454, "dur":535068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701078526, "dur":2791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701081320, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701081573, "dur":4052, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701085627, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701086385, "dur":5416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701091805, "dur":1192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701093010, "dur":9549, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701102562, "dur":1727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701104327, "dur":7181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701111508, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701111939, "dur":17167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701129110, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701129989, "dur":18235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701148225, "dur":1751, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701150000, "dur":2214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701152216, "dur":198, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701152431, "dur":4680, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701157112, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701157176, "dur":6977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701164154, "dur":1968, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701166147, "dur":6470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701172619, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701172849, "dur":12687, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871701185536, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701185844, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701185956, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701186943, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701187022, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701187159, "dur":1014, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701188197, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701188294, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701188556, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/FMODUnity.pdb" }}
,{ "pid":12345, "tid":7, "ts":1748871701188679, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701189231, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701189601, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701189713, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701189800, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701189894, "dur":925, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871701190835, "dur":3675722, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871704868395, "dur":3383, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871704866573, "dur":20476, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871704889331, "dur":726, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871705762192, "dur":1104, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871704890293, "dur":873024, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1748871705767469, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871705767458, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":7, "ts":1748871705767555, "dur":184, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748871705767781, "dur":294, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700035160, "dur":103654, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700138819, "dur":592, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700139499, "dur":1015, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700140541, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700140647, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700140722, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700140823, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700140882, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700140988, "dur":1932, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700142933, "dur":1720, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700144655, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700144824, "dur":704, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700145528, "dur":856, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700146412, "dur":719, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700147132, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700147617, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700147723, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700147878, "dur":958, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700148836, "dur":819, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700149665, "dur":6607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700156273, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700156373, "dur":458, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700156832, "dur":866, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700157712, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700157802, "dur":1385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700159188, "dur":1258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700160466, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700160525, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700160629, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700160688, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700160836, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700160897, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700160988, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700161158, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700161230, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700161307, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700161559, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162228, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162475, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162547, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162643, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162696, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162774, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700162910, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163069, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163283, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163343, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163430, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163599, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163660, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163730, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700163812, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164064, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164160, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164235, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164369, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164430, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164527, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164630, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700164853, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700165004, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700165176, "dur":2245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700167439, "dur":781, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700168223, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700168399, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700168655, "dur":1663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748871700170319, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700172458, "dur":791, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748871700173426, "dur":1981, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/fmod_studio.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700175407, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/fmod_errors.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700176286, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/FMODRuntimeManagerOnGUIHelper.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700177027, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/FMODEventTrack.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700177601, "dur":1061, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/src/FMODEventPlayable.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700179437, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/platforms/visionos/src/PlatformVisionOS.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700180107, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Assets/Plugins/FMOD/platforms/tvos/src/PlatformAppleTV.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700173249, "dur":7973, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700181900, "dur":1284, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700183185, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700181222, "dur":3757, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700185612, "dur":1183, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Text.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700187127, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700188523, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700189323, "dur":3546, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700184979, "dur":8725, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700193705, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700194804, "dur":998, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.ComponentModel.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700195803, "dur":1813, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700193704, "dur":4574, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700198575, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.HostFiltering.dll" }}
,{ "pid":12345, "tid":8, "ts":1748871700198278, "dur":2096, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700201460, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/TMP/TMP_EditorPanelUI.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700200374, "dur":2331, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700203686, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/AssemblyInfo.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700202705, "dur":2403, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700205349, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/Targets/UniversalSixWaySubTarget.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700207203, "dur":1475, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/AssetCallbacks/CreateSixWayShaderGraph.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700208679, "dur":1615, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/ShaderGraph/AssetCallbacks/CreateLitShaderGraph.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700205108, "dur":5602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700212329, "dur":1710, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/GUIFramework/CommandAction.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700214527, "dur":1356, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/ScriptablePathInspector.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700215884, "dur":8031, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/ScriptablePath.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700210711, "dur":13562, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700224400, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Canvas/Templates/CanvasShaderGUI.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700225295, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Canvas/CanvasData.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700226253, "dur":1334, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/Canvas/AssetCallbacks/CreateCanvasShadergraph.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700227588, "dur":1419, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/BuiltIn/Editor/ShaderUtils.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700231178, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInStructs.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700224274, "dur":7757, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700232211, "dur":3342, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Views/Slots/BooleanSlotControlView.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700235554, "dur":1954, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Views/ShaderPort.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700237774, "dur":2464, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Views/PropertyRow.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700241379, "dur":1975, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Views/HlslFunctionView.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700232031, "dur":11324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700243356, "dur":2204, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Colors/HeatmapColors.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700245561, "dur":1377, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Colors/CustomColorData.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700246938, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Colors/ColorManager.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700247488, "dur":4253, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Colors/CategoryColors.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700251741, "dur":1308, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Blackboard/SGBlackboardRow.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700253050, "dur":1603, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Blackboard/SGBlackboardField.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700243356, "dur":12209, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700255565, "dur":1051, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700256616, "dur":1481, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700258098, "dur":1447, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700259546, "dur":1227, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700260773, "dur":718, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700261492, "dur":2852, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700264346, "dur":1060, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeSubdivisionContext.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700265436, "dur":2239, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbePlacement.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700267676, "dur":3872, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.VirtualOffset.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700271919, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.Serialization.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700272685, "dur":1475, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.RenderingLayers.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700274161, "dur":2424, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.Placement.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700276586, "dur":886, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.LightTransport.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700277472, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeGIBaking.Invalidation.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700278811, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/ProbeVolume/ProbeAdjustmentVolumeEditor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700279359, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/Lighting/LightUnit/TemperatureSlider.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700264346, "dur":16241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700280629, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/ReflectionProbeManager.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700281230, "dur":1010, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/PostProcessPasses.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700282241, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Runtime/Passes/XROcclusionMeshPass.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700280587, "dur":3575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700284162, "dur":1410, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.State/StateGraphAsset.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700286323, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.State/State.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700284162, "dur":3765, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700288801, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Framework/Codebase/LiteralInspector.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700287927, "dur":2669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700291226, "dur":1334, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Ports/IUnitOutputPort.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700292560, "dur":848, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Ports/IUnitInvalidPort.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700294344, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Ports/InvalidOutput.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700290597, "dur":4316, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700296002, "dur":962, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Scalar/ScalarMultiply.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700299022, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/MoveTowards.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700294914, "dur":4763, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700299677, "dur":1968, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/GlobalEventUnit.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700302242, "dur":2320, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/CustomEventArgs.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700304885, "dur":1484, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Application/OnApplicationLostFocus.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700306370, "dur":1580, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Application/OnApplicationFocus.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700307950, "dur":3037, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Animation/OnAnimatorMove.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700310988, "dur":1583, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Events/Animation/OnAnimatorIK.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700299677, "dur":14196, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700314074, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/WidgetProvider.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700314677, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/WidgetAttribute.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700315680, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/StickyNote/StickyNoteEditor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700316506, "dur":732, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/Nodes/NodeWidget.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700317374, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/Nodes/NodeColorMix.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700317991, "dur":1012, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Widgets/Nodes/NodeColor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700313874, "dur":6210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700320084, "dur":1878, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700321963, "dur":3415, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700325435, "dur":1038, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Assignment/IAssigner.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700326928, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Assignment/Assigner.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700329512, "dur":1011, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Analysis/IAnalysis.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700330524, "dur":1465, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Analysis/IAnalyser.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700332602, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Analysis/AnalyserProvider.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700325379, "dur":8122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700333659, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Stripping/RenderPipelineGraphicsSettingsStripper.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700335604, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/ShaderGenerator/ShaderGeneratorAttributes.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700337859, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderPipeline/RenderPipelineGlobalSettingsUtils.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700338552, "dur":1188, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderPipeline/RenderPipelineGlobalSettings.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700339740, "dur":2631, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderPipeline/IVolumetricCloud.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700342371, "dur":3151, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderPipeline/ICloudBackground.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700345523, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/RenderGraph/RenderGraphUtilsResources.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700333501, "dur":12713, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700347312, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/DebugDisplayStats.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700348136, "dur":2015, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/DebugDisplaySettingsVolumes.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700350151, "dur":3064, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/DebugDisplaySettingsUI.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700353215, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Debugging/DebugDisplaySettingsStats.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700354304, "dur":1045, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Runtime/Common/ReloadGroupAttribute.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700346215, "dur":9252, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700356762, "dur":1033, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/SubtractionHandler.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700357935, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/PlusHandler.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700359010, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Operators/OperatorUtility.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700355467, "dur":4373, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700359841, "dur":1041, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Graphs/IGraphItem.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700361395, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Graphs/IGraphElementDebugData.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700359841, "dur":3207, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700363951, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Collections/NonNullableCollection.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700365160, "dur":2182, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Collections/INotifyCollectionChanged.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700363048, "dur":4518, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700368782, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.timeline@c58b4ee65782/Runtime/Events/Signals/SignalAsset.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700367566, "dur":2488, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700370804, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Plugins/XR/Devices/Oculus.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700370054, "dur":2173, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700372835, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/PropertyDrawers/InputActionReferenceSearchProviders.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700373434, "dur":2699, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/PropertyDrawers/InputActionReferencePropertyDrawer.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700376624, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/PropertyDrawers/InputActionMapDrawer.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700377327, "dur":2645, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Editor/PropertyDrawers/InputActionDrawerBase.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700372227, "dur":8023, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700380905, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/Processors/ScaleVector2Processor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700381778, "dur":3360, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/Processors/ScaleProcessor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700385138, "dur":2062, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/Processors/NormalizeVector3Processor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700387200, "dur":1036, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/InputSystem/Controls/Processors/NormalizeVector2Processor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700380250, "dur":9368, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700389618, "dur":1057, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/double4x3.gen.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700389618, "dur":2995, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700393016, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/CreateWorkspace/Dialogs/RepositoriesListHeaderState.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700392613, "dur":2075, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700394999, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Gluon/UpdateReport/UpdateReportListHeaderState.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700396062, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Gluon/UpdateProgress.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700398073, "dur":1334, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/ExternalLink.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700399408, "dur":935, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/EnumExtensions.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700400343, "dur":1617, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/DrawGuiModeSwitcher.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700394689, "dur":7272, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700401965, "dur":410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb" }}
,{ "pid":12345, "tid":8, "ts":1748871700402375, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700402533, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700404351, "dur":1302, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ai.navigation@c74b2724ed42/Editor/ConversionSystem/AssemblyInfo.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700406735, "dur":1114, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/ScrollRectEditor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700407849, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.ugui@03407c6d8751/Editor/UGUI/UI/ScrollbarEditor.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700402608, "dur":5936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700409352, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/Tasks/Events/RegisterCallbackDelegatorEventsTask.cs" }}
,{ "pid":12345, "tid":8, "ts":1748871700408545, "dur":2035, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700410580, "dur":2579, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700413162, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700413323, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700413822, "dur":792, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700414616, "dur":2016, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700416633, "dur":125, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700417133, "dur":14758, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700431894, "dur":1692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700433587, "dur":135, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700433823, "dur":6666, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700440490, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700440913, "dur":857, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700441771, "dur":1640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700443418, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700443806, "dur":688, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700444516, "dur":922, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700445438, "dur":871, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700446310, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700446408, "dur":7833, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700454241, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700454467, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700454583, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700454783, "dur":235, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700455031, "dur":8018, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700463049, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700463395, "dur":11533, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700474995, "dur":7700, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700482696, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700482870, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700482967, "dur":297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700483265, "dur":15625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700498953, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700499256, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700499422, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700499547, "dur":1829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700501377, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700501445, "dur":583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700502028, "dur":450, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700502482, "dur":10904, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700513387, "dur":6736, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700520172, "dur":891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700521063, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700521183, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700521406, "dur":756, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700522178, "dur":884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748871700523062, "dur":3031, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700526109, "dur":11446, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700537555, "dur":5645, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871700543202, "dur":623, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748871700543854, "dur":535883, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701079741, "dur":3399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701083142, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701083346, "dur":3669, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701087016, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701087261, "dur":6578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701093850, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701093965, "dur":7124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701101090, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701101374, "dur":8016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701109392, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701109448, "dur":3546, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701112994, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701113082, "dur":26209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701139291, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701139356, "dur":2869, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701142225, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748871701142534, "dur":49316, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1748871701191878, "dur":4576331, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748871705772488, "dur":2251, "ph":"X", "name": "ProfilerWriteOutput" }
,