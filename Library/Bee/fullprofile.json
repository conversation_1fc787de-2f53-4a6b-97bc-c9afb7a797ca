{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1427, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1427, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1427, "tid": 4688, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1427, "tid": 4688, "ts": 1748895094872384, "dur": 869, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094884074, "dur": 977, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1427, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1427, "tid": 1, "ts": 1748895093082727, "dur": 16630, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748895093099361, "dur": 195163, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748895093294542, "dur": 157721, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094885057, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 1427, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093080080, "dur": 4569, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093084651, "dur": 1760154, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093085371, "dur": 3837, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093089218, "dur": 1664, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093090884, "dur": 10717, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093101604, "dur": 426, "ph": "X", "name": "ProcessMessages 2649", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102032, "dur": 29, "ph": "X", "name": "ReadAsync 2649", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102062, "dur": 3, "ph": "X", "name": "ProcessMessages 8135", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102066, "dur": 21, "ph": "X", "name": "ReadAsync 8135", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102090, "dur": 329, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102420, "dur": 3, "ph": "X", "name": "ProcessMessages 8173", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102425, "dur": 32, "ph": "X", "name": "ReadAsync 8173", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102459, "dur": 19, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102610, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102612, "dur": 22, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102635, "dur": 2, "ph": "X", "name": "ProcessMessages 4282", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102638, "dur": 199, "ph": "X", "name": "ReadAsync 4282", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102838, "dur": 3, "ph": "X", "name": "ProcessMessages 5595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102842, "dur": 18, "ph": "X", "name": "ReadAsync 5595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102862, "dur": 17, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093102881, "dur": 209, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103092, "dur": 20, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103113, "dur": 2, "ph": "X", "name": "ProcessMessages 5646", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103116, "dur": 320, "ph": "X", "name": "ReadAsync 5646", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103437, "dur": 1, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103439, "dur": 340, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103781, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103782, "dur": 38, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103822, "dur": 1, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103823, "dur": 22, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103848, "dur": 27, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103876, "dur": 9, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103886, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103908, "dur": 25, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103936, "dur": 51, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103995, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093103996, "dur": 17, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104016, "dur": 27, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104045, "dur": 166, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104213, "dur": 21, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104236, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104258, "dur": 31, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104292, "dur": 336, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104629, "dur": 2, "ph": "X", "name": "ProcessMessages 5026", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104632, "dur": 24, "ph": "X", "name": "ReadAsync 5026", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104659, "dur": 33, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104693, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104694, "dur": 195, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104892, "dur": 24, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104917, "dur": 26, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104945, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104964, "dur": 26, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093104992, "dur": 41, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105034, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105035, "dur": 26, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105063, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105096, "dur": 30, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105128, "dur": 28, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105158, "dur": 29, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105189, "dur": 36, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093105227, "dur": 1238, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106467, "dur": 26, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106642, "dur": 4, "ph": "X", "name": "ProcessMessages 8176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106648, "dur": 20, "ph": "X", "name": "ReadAsync 8176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106668, "dur": 1, "ph": "X", "name": "ProcessMessages 1964", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106670, "dur": 278, "ph": "X", "name": "ReadAsync 1964", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106949, "dur": 2, "ph": "X", "name": "ProcessMessages 4120", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106952, "dur": 18, "ph": "X", "name": "ReadAsync 4120", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093106973, "dur": 42, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107017, "dur": 17, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107036, "dur": 335, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107373, "dur": 3, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107377, "dur": 16, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107396, "dur": 17, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107418, "dur": 30, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107449, "dur": 31, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107483, "dur": 15, "ph": "X", "name": "ReadAsync 1264", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107500, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107518, "dur": 213, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107732, "dur": 2, "ph": "X", "name": "ProcessMessages 5256", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107735, "dur": 21, "ph": "X", "name": "ReadAsync 5256", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107759, "dur": 18, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107779, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107797, "dur": 37, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107836, "dur": 42, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107880, "dur": 22, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093107904, "dur": 178, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108083, "dur": 2, "ph": "X", "name": "ProcessMessages 4216", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108085, "dur": 37, "ph": "X", "name": "ReadAsync 4216", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108125, "dur": 26, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108153, "dur": 29, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108184, "dur": 207, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108396, "dur": 2, "ph": "X", "name": "ProcessMessages 4788", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108400, "dur": 36, "ph": "X", "name": "ReadAsync 4788", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108437, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108438, "dur": 18, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108458, "dur": 19, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108479, "dur": 17, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108655, "dur": 25, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108681, "dur": 2, "ph": "X", "name": "ProcessMessages 4498", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108683, "dur": 17, "ph": "X", "name": "ReadAsync 4498", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108703, "dur": 19, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108724, "dur": 24, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108749, "dur": 23, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093108776, "dur": 337, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093109114, "dur": 3, "ph": "X", "name": "ProcessMessages 7231", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093109691, "dur": 20, "ph": "X", "name": "ReadAsync 7231", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093109711, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093109715, "dur": 319, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110036, "dur": 3, "ph": "X", "name": "ProcessMessages 7666", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110039, "dur": 17, "ph": "X", "name": "ReadAsync 7666", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110058, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110076, "dur": 16, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110094, "dur": 172, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110268, "dur": 1, "ph": "X", "name": "ProcessMessages 3245", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110270, "dur": 393, "ph": "X", "name": "ReadAsync 3245", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110664, "dur": 3, "ph": "X", "name": "ProcessMessages 5922", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110668, "dur": 20, "ph": "X", "name": "ReadAsync 5922", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110691, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110712, "dur": 19, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110734, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110755, "dur": 16, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110773, "dur": 222, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093110997, "dur": 18, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111019, "dur": 203, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111224, "dur": 521, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111747, "dur": 1, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111748, "dur": 170, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111921, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093111969, "dur": 212, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112184, "dur": 228, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112413, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112414, "dur": 15, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112431, "dur": 193, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112632, "dur": 43, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112677, "dur": 299, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093112978, "dur": 2412, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093115392, "dur": 9, "ph": "X", "name": "ProcessMessages 7268", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093115403, "dur": 141, "ph": "X", "name": "ReadAsync 7268", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093115546, "dur": 483, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093116030, "dur": 1, "ph": "X", "name": "ProcessMessages 1996", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093116032, "dur": 1474, "ph": "X", "name": "ReadAsync 1996", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093117507, "dur": 3, "ph": "X", "name": "ProcessMessages 7223", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093117512, "dur": 29, "ph": "X", "name": "ReadAsync 7223", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093117543, "dur": 45, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093117751, "dur": 193, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093117948, "dur": 85, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118035, "dur": 32, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118069, "dur": 330, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118400, "dur": 1, "ph": "X", "name": "ProcessMessages 2724", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118402, "dur": 432, "ph": "X", "name": "ReadAsync 2724", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118835, "dur": 1, "ph": "X", "name": "ProcessMessages 1596", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093118837, "dur": 393, "ph": "X", "name": "ReadAsync 1596", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093119231, "dur": 1, "ph": "X", "name": "ProcessMessages 2113", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093119233, "dur": 191, "ph": "X", "name": "ReadAsync 2113", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093119426, "dur": 191, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093119619, "dur": 377, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093119999, "dur": 42, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093120044, "dur": 46, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093120091, "dur": 11, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093120103, "dur": 142, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093120674, "dur": 310, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093120986, "dur": 769, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093121924, "dur": 21, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093121947, "dur": 446, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093122396, "dur": 798, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093123197, "dur": 89, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093123288, "dur": 643, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093123934, "dur": 1031, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093124968, "dur": 17, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093124987, "dur": 1015, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093126004, "dur": 268, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093126274, "dur": 1284, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093127561, "dur": 4, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093127565, "dur": 214, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093127782, "dur": 14, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093127798, "dur": 197, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093127996, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128014, "dur": 403, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128420, "dur": 63, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128485, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128510, "dur": 188, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128699, "dur": 1, "ph": "X", "name": "ProcessMessages 2053", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093128701, "dur": 529, "ph": "X", "name": "ReadAsync 2053", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129231, "dur": 1, "ph": "X", "name": "ProcessMessages 1800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129233, "dur": 82, "ph": "X", "name": "ReadAsync 1800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129317, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129339, "dur": 95, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129436, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129455, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129475, "dur": 27, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129504, "dur": 17, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129523, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129579, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129608, "dur": 15, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129626, "dur": 321, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129948, "dur": 1, "ph": "X", "name": "ProcessMessages 1607", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093129950, "dur": 126, "ph": "X", "name": "ReadAsync 1607", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130077, "dur": 13, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130091, "dur": 27, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130120, "dur": 24, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130147, "dur": 18, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130167, "dur": 510, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130679, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130681, "dur": 25, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130707, "dur": 1, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130709, "dur": 167, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130878, "dur": 107, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093130988, "dur": 219, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131208, "dur": 1, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131209, "dur": 253, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131464, "dur": 1, "ph": "X", "name": "ProcessMessages 1525", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131465, "dur": 183, "ph": "X", "name": "ReadAsync 1525", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131650, "dur": 1, "ph": "X", "name": "ProcessMessages 1692", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131652, "dur": 310, "ph": "X", "name": "ReadAsync 1692", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093131970, "dur": 229, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093132201, "dur": 764, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093132966, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093132968, "dur": 34, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093133003, "dur": 5, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093134357, "dur": 283, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093134642, "dur": 4, "ph": "X", "name": "ProcessMessages 1392", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093134647, "dur": 188, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093134837, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093134840, "dur": 211, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093135053, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093135055, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093135083, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093135117, "dur": 235967, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093371094, "dur": 739, "ph": "X", "name": "ProcessMessages 5560", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093371840, "dur": 79011, "ph": "X", "name": "ReadAsync 5560", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093450856, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093450858, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093450885, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093451556, "dur": 28, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093451587, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093451615, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093451636, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093451660, "dur": 12739, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093464402, "dur": 91, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093464499, "dur": 5, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093464505, "dur": 1295, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093465803, "dur": 1204, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093467011, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093467014, "dur": 931, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093467948, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093468010, "dur": 932, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093468945, "dur": 66, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093469013, "dur": 6260, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093475278, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093475279, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093475439, "dur": 156, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093476273, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093476277, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093476385, "dur": 12263, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093488661, "dur": 7, "ph": "X", "name": "ProcessMessages 2144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895093488669, "dur": 1342170, "ph": "X", "name": "ReadAsync 2144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895094830862, "dur": 66, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895094830932, "dur": 4613, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895094835555, "dur": 3537, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748895094839107, "dur": 5633, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094885078, "dur": 1425, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1427, "tid": 8589934592, "ts": 1748895093065342, "dur": 386934, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748895093452278, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748895093452283, "dur": 7998, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094886505, "dur": 17, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1427, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1427, "tid": 4294967296, "ts": 1748895092826123, "dur": 2023859, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748895092857467, "dur": 184060, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748895094858802, "dur": 7708, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748895094863562, "dur": 73, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748895094866644, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094886524, "dur": 45, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748895093075520, "dur": 5888, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895093081415, "dur": 20067, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895093101535, "dur": 81, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748895093101616, "dur": 86, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895093102133, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748895093102558, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748895093105835, "dur": 880, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748895093107500, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748895093109283, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748895093109590, "dur": 354, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748895093101709, "dur": 30247, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895093131965, "dur": 1702351, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094834422, "dur": 98, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094834520, "dur": 54, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094834587, "dur": 63, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094834665, "dur": 62, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094835142, "dur": 121, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094835402, "dur": 3516, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748895093101666, "dur": 30303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093131972, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748895093132189, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093132404, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093133066, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093133342, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093133475, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748895093133571, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748895093133846, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093134042, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093134283, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093134580, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093134714, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093134805, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748895093134931, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093135045, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093135208, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093135286, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093135662, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093136348, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093136736, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093137910, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093138761, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093139780, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093140892, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093141963, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093142914, "dur": 2059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093144974, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093145771, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093146550, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093147899, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093148882, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093150361, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093151401, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093152143, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093152887, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093153986, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093155332, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093156535, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093157801, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093158927, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093160357, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093161227, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093162343, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093163212, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093164233, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093165230, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093166067, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093166917, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093167857, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093168651, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093169505, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093170149, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093170817, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093171525, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093172433, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093173187, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093174031, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093174954, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093175261, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093176112, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093176463, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093176877, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093177736, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093177927, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093178075, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093178134, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093178439, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093178803, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093179109, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093179465, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093179622, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093180184, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093180963, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093181026, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093181120, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093181658, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093181807, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093182539, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093182731, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093183305, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093183398, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093183809, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093184132, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093184260, "dur": 5733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093190045, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093190556, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093190668, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093190726, "dur": 2644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093193371, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748895093193559, "dur": 259778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093453337, "dur": 5206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093458543, "dur": 862, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093459410, "dur": 1829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093461239, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093461666, "dur": 2091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093463772, "dur": 2802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093466586, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093469264, "dur": 3634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093472899, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748895093473325, "dur": 4658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093478015, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093479694, "dur": 4290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748895093484001, "dur": 1350227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093101667, "dur": 30307, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093131976, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748895093132186, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093132476, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093132593, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093133099, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093133354, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093133696, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748895093133832, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093133957, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748895093134206, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093134292, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748895093134523, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093134670, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093134793, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093134860, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093134913, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135027, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093135188, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135245, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135369, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135492, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135588, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093135667, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093136147, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748895093137034, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093137225, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093137728, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093138556, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093139532, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093140643, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093141745, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093142774, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093144790, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093145665, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093146417, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093147578, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093148701, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093150154, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093151249, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093151997, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093152693, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093153653, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093154948, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093156006, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093157319, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093158390, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093159515, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093160901, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093161871, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093162824, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093163847, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093164895, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093164958, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093165856, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093166680, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093167608, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093168418, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093169259, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093169935, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093170596, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093171307, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093172070, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093172926, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093173759, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093174612, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093175449, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093176570, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093177131, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093177217, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093178323, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093178837, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093179065, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093181421, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093181503, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093182814, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093182909, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093184188, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093184470, "dur": 1817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093186288, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093186744, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093186834, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093187552, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093187641, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093187962, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093188198, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093188321, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093188401, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093188652, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093189915, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093190389, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093190720, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748895093191041, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093191549, "dur": 261647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093453198, "dur": 3518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093456718, "dur": 845, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093457580, "dur": 5097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093462679, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093463265, "dur": 1893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093465159, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093465255, "dur": 3787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093469088, "dur": 3925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093473035, "dur": 3198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093476234, "dur": 767, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093477007, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093479390, "dur": 4247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748895093483756, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748895093483850, "dur": 1350526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093101674, "dur": 30304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093131979, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093132479, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093133326, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748895093133521, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093133666, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093136412, "dur": 1272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093137781, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093138641, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093139636, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093140774, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093141849, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093142850, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093144838, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093145694, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093146455, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093147731, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093148757, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093150215, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093151287, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093152014, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093152705, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093153665, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093154996, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093156013, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093157337, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093158452, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093159760, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093160978, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093162042, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093162955, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093163960, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093164905, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093165768, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093166611, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093167545, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093168358, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093169227, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093169908, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093170576, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093171277, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093171955, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093172880, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093173705, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093174565, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093175555, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093177694, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093178011, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093178086, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093179382, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093179647, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093179773, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093179857, "dur": 5864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093185722, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093185962, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093186931, "dur": 3156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093190088, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093190711, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093191566, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093191928, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093192519, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093192662, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895093193036, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093193368, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748895093193605, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748895094830637, "dur": 55, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748895093195498, "dur": 1635263, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093101680, "dur": 30301, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093131983, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093132547, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093132972, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093133105, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093133333, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748895093133631, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748895093133872, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748895093134181, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748895093134377, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748895093134669, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093134777, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093134832, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093134900, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093135018, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093135095, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093135228, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093135324, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093135469, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748895093137028, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093137157, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093137646, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093137770, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093138625, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093139619, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093140728, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093141886, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093142867, "dur": 2013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093144880, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093145728, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093146470, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093147740, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093148761, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093150241, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093151324, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093152051, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093152755, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093153708, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093155017, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093156022, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093157351, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093158460, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093159757, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093160971, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093162037, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093162945, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093163933, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093164900, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093165740, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093166593, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093167503, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093168303, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093169187, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093169869, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093170525, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093171253, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093171915, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093172861, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093173666, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093174532, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093175500, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093176289, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093176727, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093177195, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093178503, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093178786, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093178953, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748895093180010, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093180075, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748895093181223, "dur": 256, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093414160, "dur": 37707, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093181607, "dur": 270286, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748895093453168, "dur": 3963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093457132, "dur": 1113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093458304, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093461053, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093461719, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093464272, "dur": 3271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093467544, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093467626, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093470143, "dur": 2036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093472192, "dur": 2812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093475004, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093475726, "dur": 4029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093479803, "dur": 3976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748895093483807, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748895093484003, "dur": 1350229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093101685, "dur": 30316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093132005, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093132649, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133099, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133222, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133404, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133510, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133682, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093133965, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748895093134242, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093134399, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093134574, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093134767, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093134836, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093135080, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093135225, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093135319, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748895093135685, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093135749, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093136343, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748895093137554, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093138354, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093139146, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093140379, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093141369, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093142534, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093144480, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093145395, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093146207, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093147231, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093148411, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093149937, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093151063, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093151787, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093152520, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093153372, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093154640, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093155735, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093157043, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093158158, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093159303, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093160748, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093161602, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093162657, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093163602, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093164585, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093165480, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093166270, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093167221, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093168074, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093168942, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093169662, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093170310, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093171027, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093171696, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093172629, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093173373, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093174212, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093175180, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093176269, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093177374, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093177469, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093178433, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093178538, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093178845, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093179124, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093179466, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093179727, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093180688, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093181005, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093181960, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093182121, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093182237, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093184875, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093185143, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093185697, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093186863, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093187613, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D2CC97DA1342C9A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093187774, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093188057, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093188608, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093188685, "dur": 1707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093190392, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093190737, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093190795, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093190972, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748895093191231, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093191535, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093192104, "dur": 261089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093453193, "dur": 5728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093458922, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093459354, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093462077, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093464265, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093464561, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093467072, "dur": 1904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093468976, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093469164, "dur": 4641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093473856, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093476045, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093479043, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748895093479110, "dur": 4761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748895093483927, "dur": 1350488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093101692, "dur": 30316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093132010, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093132388, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093132472, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093133054, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093133363, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748895093133439, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748895093133709, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748895093133837, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748895093134039, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748895093134241, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748895093134501, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748895093134606, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093134664, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748895093134854, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093134907, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135025, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135242, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135351, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135488, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135652, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093135795, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748895093136365, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748895093137122, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093137541, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093138358, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093139172, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093140260, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093141325, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093142461, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093144437, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093145368, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093146191, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093147220, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093148406, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093149949, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093151049, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093151783, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093152515, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093153345, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093154602, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093155710, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093157013, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093158134, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093159271, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093160713, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093161570, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093162631, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093163582, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093164583, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093165469, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093166267, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093167277, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093168063, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093168891, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093169617, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093170275, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093170976, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093171664, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093172610, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093173351, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093174207, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093175072, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093175481, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093175582, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093176002, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093176276, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093176421, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093176819, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093177294, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093177494, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093177569, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093178521, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093178595, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093178663, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093178737, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093178858, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093179138, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093180452, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093180615, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093180711, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093180818, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093181287, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093181393, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093181571, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093182484, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093182667, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093182767, "dur": 3669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093186436, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093186587, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748895093186659, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093188851, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093188949, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093189381, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093189888, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093190693, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093190808, "dur": 262386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093453196, "dur": 4902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093458100, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093458275, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093458563, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093461256, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093461620, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093463198, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093463259, "dur": 2174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093465475, "dur": 2711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093468200, "dur": 4750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093472951, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093473550, "dur": 3024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093476575, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093477110, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093479865, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093479957, "dur": 3608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748895093483646, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093483759, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748895093483925, "dur": 1350467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093101697, "dur": 30322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093132022, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093132477, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093132817, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093133110, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093133192, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748895093133475, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093133548, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093136734, "dur": 889, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093137667, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093137726, "dur": 2821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093140548, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093140617, "dur": 23844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093164465, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093164711, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093164833, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093167592, "dur": 6529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093174122, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093174404, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093174469, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093175299, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093177500, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093177756, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093178145, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093178479, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093179151, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093179231, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093180080, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093180394, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093180473, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093180939, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093181792, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093182971, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093183794, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093184303, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093184409, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093184688, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093185673, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093185976, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093186155, "dur": 3635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093189790, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093190027, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093190413, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093190722, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748895093191592, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093192584, "dur": 260625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093453210, "dur": 4438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093457649, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093457739, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093460580, "dur": 851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093461740, "dur": 2171, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1748895093463911, "dur": 5115, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1748895093469027, "dur": 752, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1748895093461436, "dur": 8343, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093469780, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093473274, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093473531, "dur": 2207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093475738, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093475798, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093477963, "dur": 2833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748895093480797, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093483353, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093483678, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748895093483814, "dur": 1350554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093101704, "dur": 30322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093132026, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093132396, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093132582, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093133012, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093133187, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748895093133436, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093133611, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093133768, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093134058, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093134487, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093134590, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093134733, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093134868, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093134928, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135037, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135101, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135247, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135374, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135436, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093135661, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093136316, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093136620, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748895093137066, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093137471, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093138232, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093139062, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093140282, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093141289, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093142391, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093144201, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093145323, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093146121, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093147130, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093148348, "dur": 1583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093149933, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093151019, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093151769, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093152496, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093153340, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093154594, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093155712, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093157037, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093158142, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093159298, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093160743, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093161594, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093162641, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093163588, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093164606, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093165501, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093166282, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093167288, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093168122, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093168971, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093169687, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093170344, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093171036, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093171707, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093172673, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093173416, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093174289, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093175144, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093175394, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093176759, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093176849, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093177446, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093178113, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093178894, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093179414, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093179677, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093179971, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093180036, "dur": 1550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093181587, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093181946, "dur": 992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093182939, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093183089, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093183160, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093183598, "dur": 3536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093187134, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093187208, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093187263, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748895093187790, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093189227, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093189605, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093189827, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093190030, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093190416, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093190728, "dur": 262444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093453175, "dur": 6892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093460068, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093460129, "dur": 2717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093462897, "dur": 2595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093465528, "dur": 1827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093467404, "dur": 4688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093472093, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093472209, "dur": 3246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093475498, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093477788, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093479583, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748895093479690, "dur": 4135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748895093483842, "dur": 1350508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748895094842830, "dur": 1328, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1427, "tid": 4688, "ts": 1748895094890281, "dur": 6327, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1427, "tid": 4688, "ts": 1748895094896821, "dur": 2764, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1427, "tid": 4688, "ts": 1748895094879704, "dur": 23687, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}