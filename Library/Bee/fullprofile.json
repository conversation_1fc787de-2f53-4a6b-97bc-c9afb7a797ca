{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1427, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1427, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1427, "tid": 4372, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1427, "tid": 4372, "ts": 1748893656954599, "dur": 482, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656962118, "dur": 805, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1427, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1427, "tid": 1, "ts": 1748893655626461, "dur": 17275, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748893655643739, "dur": 284903, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748893655928668, "dur": 194927, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656962927, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 1427, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655623272, "dur": 719, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655623995, "dur": 1316981, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655625055, "dur": 3989, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655629047, "dur": 866, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655629915, "dur": 10923, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655640841, "dur": 352, "ph": "X", "name": "ProcessMessages 1783", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641195, "dur": 343, "ph": "X", "name": "ReadAsync 1783", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641540, "dur": 4, "ph": "X", "name": "ProcessMessages 8114", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641545, "dur": 25, "ph": "X", "name": "ReadAsync 8114", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641571, "dur": 3, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641575, "dur": 210, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641787, "dur": 25, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641813, "dur": 2, "ph": "X", "name": "ProcessMessages 5587", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641816, "dur": 19, "ph": "X", "name": "ReadAsync 5587", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641838, "dur": 158, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641996, "dur": 2, "ph": "X", "name": "ProcessMessages 4241", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655641999, "dur": 18, "ph": "X", "name": "ReadAsync 4241", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642020, "dur": 41, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642063, "dur": 1, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642064, "dur": 191, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642256, "dur": 2, "ph": "X", "name": "ProcessMessages 5432", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642260, "dur": 17, "ph": "X", "name": "ReadAsync 5432", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642279, "dur": 179, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642460, "dur": 1, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642463, "dur": 383, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642847, "dur": 75, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642924, "dur": 18, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642943, "dur": 5, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642952, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655642978, "dur": 656, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643636, "dur": 41, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643678, "dur": 3, "ph": "X", "name": "ProcessMessages 8057", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643683, "dur": 16, "ph": "X", "name": "ReadAsync 8057", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643701, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643725, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643727, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643744, "dur": 193, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643939, "dur": 2, "ph": "X", "name": "ProcessMessages 4153", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655643942, "dur": 122, "ph": "X", "name": "ReadAsync 4153", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644067, "dur": 194, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644265, "dur": 2, "ph": "X", "name": "ProcessMessages 3980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644268, "dur": 68, "ph": "X", "name": "ReadAsync 3980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644339, "dur": 44, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644385, "dur": 1, "ph": "X", "name": "ProcessMessages 2179", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644387, "dur": 31, "ph": "X", "name": "ReadAsync 2179", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644420, "dur": 367, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644794, "dur": 4, "ph": "X", "name": "ProcessMessages 8176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644799, "dur": 18, "ph": "X", "name": "ReadAsync 8176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644820, "dur": 20, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644841, "dur": 21, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655644864, "dur": 201, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645067, "dur": 22, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645091, "dur": 22, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645115, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645133, "dur": 44, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645180, "dur": 36, "ph": "X", "name": "ReadAsync 1243", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645218, "dur": 41, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645261, "dur": 17, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645281, "dur": 24, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645311, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645331, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645354, "dur": 4, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645359, "dur": 18, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645380, "dur": 17, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645410, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645432, "dur": 199, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645632, "dur": 2, "ph": "X", "name": "ProcessMessages 5559", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645635, "dur": 16, "ph": "X", "name": "ReadAsync 5559", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645652, "dur": 30, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645684, "dur": 20, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645706, "dur": 22, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645729, "dur": 21, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645753, "dur": 33, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645788, "dur": 39, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645837, "dur": 29, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655645868, "dur": 186, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646055, "dur": 2, "ph": "X", "name": "ProcessMessages 4936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646058, "dur": 15, "ph": "X", "name": "ReadAsync 4936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646074, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646094, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646114, "dur": 28, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646144, "dur": 26, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646172, "dur": 25, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646199, "dur": 39, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646241, "dur": 18, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646261, "dur": 159, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646425, "dur": 2, "ph": "X", "name": "ProcessMessages 3561", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646428, "dur": 16, "ph": "X", "name": "ReadAsync 3561", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646456, "dur": 37, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646495, "dur": 1, "ph": "X", "name": "ProcessMessages 1325", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646496, "dur": 15, "ph": "X", "name": "ReadAsync 1325", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646513, "dur": 325, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646839, "dur": 3, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646842, "dur": 16, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646860, "dur": 40, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655646903, "dur": 171, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647075, "dur": 2, "ph": "X", "name": "ProcessMessages 4210", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647077, "dur": 161, "ph": "X", "name": "ReadAsync 4210", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647239, "dur": 2, "ph": "X", "name": "ProcessMessages 3621", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647242, "dur": 17, "ph": "X", "name": "ReadAsync 3621", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647261, "dur": 23, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647286, "dur": 15, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647794, "dur": 20, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647816, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647820, "dur": 22, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655647844, "dur": 186, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648031, "dur": 2, "ph": "X", "name": "ProcessMessages 4410", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648034, "dur": 17, "ph": "X", "name": "ReadAsync 4410", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648053, "dur": 205, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648259, "dur": 2, "ph": "X", "name": "ProcessMessages 5447", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648262, "dur": 325, "ph": "X", "name": "ReadAsync 5447", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648588, "dur": 1, "ph": "X", "name": "ProcessMessages 3022", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648590, "dur": 20, "ph": "X", "name": "ReadAsync 3022", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648612, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648631, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648655, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648677, "dur": 17, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648696, "dur": 34, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655648732, "dur": 320, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649053, "dur": 2, "ph": "X", "name": "ProcessMessages 3413", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649055, "dur": 23, "ph": "X", "name": "ReadAsync 3413", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649086, "dur": 340, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649428, "dur": 354, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649784, "dur": 17, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649803, "dur": 149, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655649954, "dur": 199, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650155, "dur": 61, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650218, "dur": 45, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650266, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650295, "dur": 166, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650462, "dur": 190, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655650658, "dur": 396, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651058, "dur": 18, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651077, "dur": 18, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651098, "dur": 475, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651575, "dur": 41, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651618, "dur": 337, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651956, "dur": 1, "ph": "X", "name": "ProcessMessages 1504", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655651958, "dur": 138, "ph": "X", "name": "ReadAsync 1504", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652098, "dur": 62, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652162, "dur": 181, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652345, "dur": 35, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652382, "dur": 182, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652565, "dur": 6, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652572, "dur": 170, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655652744, "dur": 361, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653107, "dur": 15, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653136, "dur": 202, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653341, "dur": 194, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653537, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653555, "dur": 203, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653760, "dur": 61, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653823, "dur": 17, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655653842, "dur": 530, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655654374, "dur": 67, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655654443, "dur": 16, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655654461, "dur": 716, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655178, "dur": 1, "ph": "X", "name": "ProcessMessages 2150", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655180, "dur": 237, "ph": "X", "name": "ReadAsync 2150", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655419, "dur": 275, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655696, "dur": 20, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655717, "dur": 27, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655655746, "dur": 743, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655656492, "dur": 544, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655657037, "dur": 1, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655657040, "dur": 1342, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658384, "dur": 32, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658418, "dur": 251, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658672, "dur": 20, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658696, "dur": 15, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658718, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658738, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655658759, "dur": 856, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655659617, "dur": 323, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655659942, "dur": 400, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655660344, "dur": 322, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655660668, "dur": 200, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655660871, "dur": 16, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655660889, "dur": 39, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655660930, "dur": 545, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655661478, "dur": 13, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655661492, "dur": 575, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655662070, "dur": 54, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655662126, "dur": 208, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655662337, "dur": 690, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655663029, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655663030, "dur": 522, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655663554, "dur": 64, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655663620, "dur": 965, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655664588, "dur": 421, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655665011, "dur": 26, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655665040, "dur": 707, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655665749, "dur": 342, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655666093, "dur": 5, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655666098, "dur": 396, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655666497, "dur": 18, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655666517, "dur": 616, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655667136, "dur": 164, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655667302, "dur": 422, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655667726, "dur": 162, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655667890, "dur": 495, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655668387, "dur": 569, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655668958, "dur": 23, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655668983, "dur": 722, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655669708, "dur": 1479, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655671189, "dur": 2, "ph": "X", "name": "ProcessMessages 3123", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655671191, "dur": 678, "ph": "X", "name": "ReadAsync 3123", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655671872, "dur": 867, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655672915, "dur": 66, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655672984, "dur": 362, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655673348, "dur": 377, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655673727, "dur": 634, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674364, "dur": 19, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674385, "dur": 21, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674408, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674425, "dur": 115, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674542, "dur": 205, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674748, "dur": 1, "ph": "X", "name": "ProcessMessages 1925", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655674750, "dur": 718, "ph": "X", "name": "ReadAsync 1925", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675470, "dur": 1, "ph": "X", "name": "ProcessMessages 2884", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675472, "dur": 26, "ph": "X", "name": "ReadAsync 2884", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675500, "dur": 211, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675717, "dur": 42, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675760, "dur": 1, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675761, "dur": 19, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655675782, "dur": 755, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655676539, "dur": 1, "ph": "X", "name": "ProcessMessages 2290", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655676541, "dur": 17, "ph": "X", "name": "ReadAsync 2290", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655676560, "dur": 1281, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655677842, "dur": 1, "ph": "X", "name": "ProcessMessages 2696", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655677844, "dur": 20, "ph": "X", "name": "ReadAsync 2696", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655677866, "dur": 151, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655928117, "dur": 307, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655928427, "dur": 567, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655928996, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655929037, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655929039, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655929065, "dur": 30, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655929097, "dur": 1134, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655931133, "dur": 29, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655931165, "dur": 21, "ph": "X", "name": "ReadAsync 7587", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655931188, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655931207, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655931227, "dur": 2675, "ph": "X", "name": "ProcessMessages 2217", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655933905, "dur": 51, "ph": "X", "name": "ReadAsync 2217", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655933972, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655933974, "dur": 1662, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655935639, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655935723, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655935963, "dur": 648, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655936612, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655937112, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655937136, "dur": 725, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655937864, "dur": 2645, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655940510, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655940519, "dur": 479, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655941164, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655941365, "dur": 3152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655944520, "dur": 419, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655944941, "dur": 509, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655945452, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655945686, "dur": 3820, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655949509, "dur": 2055, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655951567, "dur": 645, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655952214, "dur": 7169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655959562, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893655959568, "dur": 182647, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656142225, "dur": 15, "ph": "X", "name": "ProcessMessages 2948", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656142241, "dur": 572040, "ph": "X", "name": "ReadAsync 2948", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656714288, "dur": 34, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656714323, "dur": 1233, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656715559, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656715561, "dur": 2950, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656718515, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656718518, "dur": 103487, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656822008, "dur": 15, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656822025, "dur": 2628, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656824658, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656824661, "dur": 65875, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890542, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890544, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890591, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890641, "dur": 75, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890719, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890779, "dur": 27, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656890806, "dur": 3212, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656894032, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656894036, "dur": 524, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656894561, "dur": 20, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656894582, "dur": 37850, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932438, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932441, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932495, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932533, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932575, "dur": 138, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932715, "dur": 17, "ph": "X", "name": "ProcessMessages 7980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656932733, "dur": 3154, "ph": "X", "name": "ReadAsync 7980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656935890, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656935893, "dur": 148, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656936043, "dur": 22, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656936065, "dur": 244, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656936310, "dur": 218, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893656936530, "dur": 4415, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656962939, "dur": 1298, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893655617775, "dur": 505852, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893656123630, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893656123638, "dur": 4881, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656964239, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1427, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893655417217, "dur": 1526484, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893655425507, "dur": 178743, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893656943767, "dur": 5598, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893656947484, "dur": 34, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893656949442, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656964245, "dur": 161, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748893655618450, "dur": 3366, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893655621821, "dur": 18981, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893655640859, "dur": 77, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748893655640937, "dur": 93, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893655641455, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893655645032, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893655647060, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893655647886, "dur": 234, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748893655641035, "dur": 36871, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893655677914, "dur": 1258367, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893656936321, "dur": 76, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893656936505, "dur": 1091, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748893655640992, "dur": 36928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655677923, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748893655678137, "dur": 9302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655687545, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655687627, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655689331, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748893655689486, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748893655691493, "dur": 2064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655693562, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748893655695757, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655695834, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655697857, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655698055, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655698144, "dur": 1692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655699883, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655701964, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655702083, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655702409, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655703011, "dur": 828, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655703848, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655703905, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655704110, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655704233, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655704294, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655704353, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655704963, "dur": 4969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655709932, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655710503, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655711725, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655712871, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655713959, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655715715, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655717285, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655721408, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655721594, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655721940, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655722668, "dur": 4029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655726697, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655726822, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655728948, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655729038, "dur": 2486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655731525, "dur": 2169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655733702, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655733786, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655733877, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655734052, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655734121, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655735854, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655736537, "dur": 191972, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893655928547, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655928765, "dur": 2795, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893655931560, "dur": 5113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655936674, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655936900, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655936992, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937118, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937272, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937423, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937523, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937592, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937696, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748893655937791, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655937900, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655938860, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655939534, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655939909, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655940540, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655941263, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655941988, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655942645, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655943283, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655943920, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655944667, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655945330, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655945503, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655945787, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655946152, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655946790, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655947411, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655947974, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655948600, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655949216, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655949913, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655950607, "dur": 4523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655955169, "dur": 3564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655958733, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655958872, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655962308, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655962511, "dur": 3715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655966226, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655966291, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655968511, "dur": 770, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655969285, "dur": 2822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655972107, "dur": 1897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655974007, "dur": 2822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655976830, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655976936, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655978829, "dur": 5772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893655984613, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655986176, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893655986352, "dur": 142314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893656128946, "dur": 1148, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748893656130094, "dur": 1901, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1748893656131995, "dur": 574, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1748893656128668, "dur": 3903, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893656132571, "dur": 803813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655640993, "dur": 36931, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655677926, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748893655678116, "dur": 9346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655687526, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655687614, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655687771, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655687859, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655687949, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655688127, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655688212, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655688359, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655688970, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655689647, "dur": 3835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655693488, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655695753, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655697819, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748893655699924, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655699996, "dur": 1900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655701903, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655702416, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655702751, "dur": 1135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655703960, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655704113, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655704248, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655704440, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893655704796, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748893655705023, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655705359, "dur": 11025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655716385, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655717065, "dur": 3321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655720387, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655720595, "dur": 14248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655734843, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655735012, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655736662, "dur": 191878, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655928544, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655929088, "dur": 2476, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655931784, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655932715, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655933593, "dur": 4055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655937648, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655937876, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655937955, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655939118, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655939735, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655939861, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655940324, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655941015, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655941762, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655942416, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655943076, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655943715, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893655945763, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655949263, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655949355, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655950071, "dur": 4304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655954375, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655954548, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655957974, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655958122, "dur": 3163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655961334, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655963182, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655963288, "dur": 5629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655968918, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655968997, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655971908, "dur": 2059, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655974014, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655977787, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655977866, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655980353, "dur": 5150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893655985677, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893655986364, "dur": 728897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893656716586, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748893656715262, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893656718468, "dur": 211, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893656890675, "dur": 338, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893656719160, "dur": 171868, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893656894084, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748893656894079, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748893656894199, "dur": 547, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748893656894747, "dur": 41588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655640999, "dur": 36930, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655677931, "dur": 9507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655687520, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655687629, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655687721, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655687915, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655688012, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655688104, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655688188, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655688900, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893655689442, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893655689560, "dur": 1951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748893655691518, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893655693700, "dur": 2148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748893655695855, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893655697906, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655698288, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655699315, "dur": 4408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655703737, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_4EAD86AD4C1B715A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655703848, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655704409, "dur": 4947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655709357, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655709734, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655710191, "dur": 10608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655720799, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655721129, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655721884, "dur": 4025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655725909, "dur": 3784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655729707, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655729781, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655729847, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655731127, "dur": 6278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655737405, "dur": 3517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655740922, "dur": 187663, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655928606, "dur": 2943, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655931551, "dur": 1687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655933269, "dur": 20005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655953275, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655953586, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655953851, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655955369, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655955521, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655955622, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655955993, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655956131, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893655956277, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893655956881, "dur": 142, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893655957901, "dur": 756548, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893656715236, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893656717328, "dur": 104849, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893656822886, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893656824660, "dur": 199, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893656932589, "dur": 286, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893656825269, "dur": 107640, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893656935792, "dur": 444, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748893656936239, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655641006, "dur": 36945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655677952, "dur": 9488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655687576, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655687754, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655687856, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655688134, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655688369, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655688891, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893655689467, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655689529, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655690520, "dur": 3085, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655693623, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655693679, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655694719, "dur": 1141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655695878, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655695965, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655696267, "dur": 1597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655697897, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655697973, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655698028, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655699100, "dur": 2882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655701998, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655702098, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655702214, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655702942, "dur": 940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655703926, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655704580, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655707160, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655707405, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655707467, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655707646, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655707835, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655708060, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893655708239, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655708760, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655708870, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655708925, "dur": 1548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655710508, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655711577, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655711994, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655712930, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655714093, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655714150, "dur": 1665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655715851, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655715990, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893655716346, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893655716515, "dur": 4091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655720607, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655720826, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655722709, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655722874, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655723499, "dur": 4721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655728221, "dur": 3376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655731601, "dur": 3544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655735145, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655735296, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893655735820, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655735876, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655737406, "dur": 194106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655931516, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655932479, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655932545, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655936115, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655936213, "dur": 4411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655940625, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655940734, "dur": 4309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655945043, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655945132, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655945807, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655945997, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655946086, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655946351, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655947041, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655947650, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655948258, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655948836, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655949482, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655950182, "dur": 6343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655956526, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655956610, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893655956754, "dur": 2766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655959521, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655959723, "dur": 6830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655966555, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655966660, "dur": 3404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655970065, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655970120, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655972253, "dur": 1693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655973951, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655976999, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655977066, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655980192, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893655980306, "dur": 2379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655982714, "dur": 3887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893655986615, "dur": 949723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655641012, "dur": 36943, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655677956, "dur": 9485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655687452, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655687589, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655687945, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655688118, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655688219, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655689321, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655691554, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655693773, "dur": 4212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655697989, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655698079, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655698155, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655698250, "dur": 1633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655700360, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655701983, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655702103, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655702353, "dur": 1391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748893655703803, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655704106, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655704216, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655704272, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655704341, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655704876, "dur": 4646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655709522, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655710120, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655710947, "dur": 3825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655714772, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655715130, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655716825, "dur": 3824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655720650, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655720724, "dur": 2018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655722742, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655722836, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655723440, "dur": 3911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655727351, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655727493, "dur": 3162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655730655, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655730756, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655733426, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655733652, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655733740, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655733809, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655733869, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655733961, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655734093, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655734162, "dur": 1752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655735914, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655736548, "dur": 191975, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748893655928544, "dur": 2979, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893655931579, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655931752, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893655933256, "dur": 3502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655936758, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655936877, "dur": 4598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655941475, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655941577, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655942296, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655942953, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655943604, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655944229, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655944941, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655945580, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655945908, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655946146, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655946425, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655947077, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655947680, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655948293, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655948886, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655949541, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655950232, "dur": 7214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655957487, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655959765, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655959833, "dur": 4427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655964262, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655964445, "dur": 4379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655968872, "dur": 3354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655972227, "dur": 3465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655975697, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655980672, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655980732, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893655983884, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655984034, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655984580, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655985738, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655985908, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655986009, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893655986357, "dur": 146216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893656132574, "dur": 803702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655641019, "dur": 36945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655677965, "dur": 9492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655687462, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655687575, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655687676, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655687863, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655687957, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655688032, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655688114, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655688200, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655688348, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655688539, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655688967, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893655689566, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655689663, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655690398, "dur": 3182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655693596, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748893655695884, "dur": 2025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893655697922, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893655698074, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655698211, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655698286, "dur": 1636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893655699973, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655700183, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655700461, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655700870, "dur": 2856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655703726, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655703876, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655703936, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655704918, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655707241, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655707648, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655708063, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655709998, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893655710328, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655710388, "dur": 2995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655713383, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655713603, "dur": 5366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1748893655719039, "dur": 2366, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655866578, "dur": 64928, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655721668, "dur": 209857, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1748893655949984, "dur": 5125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655955109, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655955262, "dur": 3363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655958626, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655958692, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655961038, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655961152, "dur": 6029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655967182, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655967258, "dur": 3862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655971122, "dur": 2819, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655973945, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655977286, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893655977369, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655978881, "dur": 3542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655982434, "dur": 4126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893655986575, "dur": 907512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893656894150, "dur": 573, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748893656894726, "dur": 41551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655641025, "dur": 36942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655677969, "dur": 9499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655687571, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655687837, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655687940, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655688038, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655688123, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655688209, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655688353, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655688888, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748893655689483, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748893655691550, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893655693768, "dur": 2053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893655695829, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893655695926, "dur": 1869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748893655697857, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655697959, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655698404, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655700962, "dur": 3033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655703999, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_62EC740DD3AB3A8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655704066, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655704124, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655704488, "dur": 14255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655718744, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655718922, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655719386, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655720951, "dur": 15910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655736862, "dur": 1240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655738102, "dur": 190462, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655928592, "dur": 2950, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655931545, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655933392, "dur": 9877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655943269, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655943658, "dur": 1852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655945546, "dur": 5685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655951231, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655951527, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893655952246, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655953126, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655953251, "dur": 3840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655957143, "dur": 3895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655961038, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655961123, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655965825, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655965888, "dur": 2885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655968774, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655969242, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655972997, "dur": 961, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655974001, "dur": 3536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655977538, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655977634, "dur": 5556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893655983190, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655983807, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655983960, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655984136, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655984972, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655985620, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655986350, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893655986579, "dur": 949187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893656935797, "dur": 459, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748893655641031, "dur": 36946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655677978, "dur": 9478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655687467, "dur": 1830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655689313, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893655689469, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893655689638, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748893655691551, "dur": 2141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893655693710, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748893655695907, "dur": 2117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748893655698025, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655698136, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655698220, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893655699973, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655700049, "dur": 1916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893655701965, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655702126, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655702389, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655702803, "dur": 969, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655703789, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655705454, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655705549, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655705656, "dur": 3979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655709636, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655710459, "dur": 11907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655722366, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655722536, "dur": 3840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655726377, "dur": 3346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655729783, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655729850, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655731124, "dur": 6170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655737294, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655737960, "dur": 190594, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655928596, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655928708, "dur": 2845, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655931554, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655932771, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655935569, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655935873, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655935962, "dur": 4878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655940840, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655940936, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655941716, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655942369, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655943018, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655943685, "dur": 1820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893655945538, "dur": 3727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655949265, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655949409, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655949472, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655950156, "dur": 4926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655955082, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655955209, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655958161, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655958312, "dur": 4125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655962437, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655963195, "dur": 4841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655968097, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655971024, "dur": 2731, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655973764, "dur": 5104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655978897, "dur": 3041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655981964, "dur": 4207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893655986231, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655986352, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893655986618, "dur": 949769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893656939344, "dur": 358, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1427, "tid": 4372, "ts": 1748893656966403, "dur": 4312, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1427, "tid": 4372, "ts": 1748893656970748, "dur": 1722, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1427, "tid": 4372, "ts": 1748893656959513, "dur": 14136, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}