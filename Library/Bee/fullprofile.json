{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1427, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1427, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1427, "tid": 4276, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1427, "tid": 4276, "ts": 1748893143064299, "dur": 911, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143079271, "dur": 1269, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1427, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1427, "tid": 1, "ts": 1748893141711624, "dur": 9773, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748893141721403, "dur": 220598, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748893141942020, "dur": 127609, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143080543, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 1427, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893141709199, "dur": 312466, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142021674, "dur": 1028016, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142025601, "dur": 10358, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142035998, "dur": 84040, "ph": "X", "name": "ProcessMessages 8124", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142120052, "dur": 233, "ph": "X", "name": "ReadAsync 8124", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142120303, "dur": 5, "ph": "X", "name": "ProcessMessages 8170", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142120309, "dur": 206, "ph": "X", "name": "ReadAsync 8170", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142121354, "dur": 4, "ph": "X", "name": "ProcessMessages 3622", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142121359, "dur": 63, "ph": "X", "name": "ReadAsync 3622", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142121424, "dur": 3, "ph": "X", "name": "ProcessMessages 5784", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142121427, "dur": 19916, "ph": "X", "name": "ReadAsync 5784", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141350, "dur": 6, "ph": "X", "name": "ProcessMessages 8097", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141357, "dur": 352, "ph": "X", "name": "ReadAsync 8097", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141825, "dur": 10, "ph": "X", "name": "ProcessMessages 4689", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141857, "dur": 87, "ph": "X", "name": "ReadAsync 4689", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141946, "dur": 4, "ph": "X", "name": "ProcessMessages 1716", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142141957, "dur": 766, "ph": "X", "name": "ReadAsync 1716", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142142741, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142142745, "dur": 187, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142142934, "dur": 1, "ph": "X", "name": "ProcessMessages 1951", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142142936, "dur": 108, "ph": "X", "name": "ReadAsync 1951", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143048, "dur": 4, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143054, "dur": 364, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143421, "dur": 14, "ph": "X", "name": "ProcessMessages 1271", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143437, "dur": 113, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143555, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143558, "dur": 117, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143677, "dur": 2, "ph": "X", "name": "ProcessMessages 1993", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143680, "dur": 177, "ph": "X", "name": "ReadAsync 1993", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143858, "dur": 1, "ph": "X", "name": "ProcessMessages 1414", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143860, "dur": 90, "ph": "X", "name": "ReadAsync 1414", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143952, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142143954, "dur": 302, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144258, "dur": 5, "ph": "X", "name": "ProcessMessages 2696", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144265, "dur": 46, "ph": "X", "name": "ReadAsync 2696", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144312, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144313, "dur": 46, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144395, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144396, "dur": 50, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144450, "dur": 69, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144519, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142144521, "dur": 491, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145016, "dur": 2, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145020, "dur": 109, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145141, "dur": 2, "ph": "X", "name": "ProcessMessages 2664", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145144, "dur": 211, "ph": "X", "name": "ReadAsync 2664", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145357, "dur": 50, "ph": "X", "name": "ProcessMessages 1484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145410, "dur": 70, "ph": "X", "name": "ReadAsync 1484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145484, "dur": 4, "ph": "X", "name": "ProcessMessages 2512", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145489, "dur": 61, "ph": "X", "name": "ReadAsync 2512", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145552, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142145553, "dur": 566, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146121, "dur": 2, "ph": "X", "name": "ProcessMessages 4808", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146125, "dur": 102, "ph": "X", "name": "ReadAsync 4808", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146228, "dur": 1, "ph": "X", "name": "ProcessMessages 1902", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146229, "dur": 48, "ph": "X", "name": "ReadAsync 1902", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146281, "dur": 41, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146323, "dur": 67, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146393, "dur": 187, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146582, "dur": 10, "ph": "X", "name": "ProcessMessages 1964", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146593, "dur": 224, "ph": "X", "name": "ReadAsync 1964", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146818, "dur": 1, "ph": "X", "name": "ProcessMessages 2565", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146820, "dur": 86, "ph": "X", "name": "ReadAsync 2565", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146910, "dur": 3, "ph": "X", "name": "ProcessMessages 1555", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146915, "dur": 48, "ph": "X", "name": "ReadAsync 1555", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146964, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146966, "dur": 28, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142146996, "dur": 39, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147038, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147042, "dur": 70, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147114, "dur": 69, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147185, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147188, "dur": 72, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147285, "dur": 3, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147290, "dur": 58, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147348, "dur": 1, "ph": "X", "name": "ProcessMessages 1191", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147350, "dur": 40, "ph": "X", "name": "ReadAsync 1191", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147392, "dur": 149, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147543, "dur": 24, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147569, "dur": 69, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147648, "dur": 219, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147868, "dur": 1, "ph": "X", "name": "ProcessMessages 2182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147870, "dur": 42, "ph": "X", "name": "ReadAsync 2182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147913, "dur": 1, "ph": "X", "name": "ProcessMessages 2042", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147915, "dur": 50, "ph": "X", "name": "ReadAsync 2042", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142147968, "dur": 112, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148082, "dur": 50, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148133, "dur": 1, "ph": "X", "name": "ProcessMessages 1962", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148135, "dur": 134, "ph": "X", "name": "ReadAsync 1962", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148270, "dur": 1, "ph": "X", "name": "ProcessMessages 1907", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148271, "dur": 55, "ph": "X", "name": "ReadAsync 1907", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148339, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148341, "dur": 28, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148371, "dur": 310, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148683, "dur": 1, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148685, "dur": 251, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148937, "dur": 2, "ph": "X", "name": "ProcessMessages 4080", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142148940, "dur": 89, "ph": "X", "name": "ReadAsync 4080", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149031, "dur": 52, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149083, "dur": 1, "ph": "X", "name": "ProcessMessages 1557", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149085, "dur": 131, "ph": "X", "name": "ReadAsync 1557", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149217, "dur": 1, "ph": "X", "name": "ProcessMessages 1980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149220, "dur": 156, "ph": "X", "name": "ReadAsync 1980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149377, "dur": 1, "ph": "X", "name": "ProcessMessages 1920", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149380, "dur": 257, "ph": "X", "name": "ReadAsync 1920", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149638, "dur": 1, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149640, "dur": 196, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149841, "dur": 15, "ph": "X", "name": "ProcessMessages 2360", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142149858, "dur": 251, "ph": "X", "name": "ReadAsync 2360", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150111, "dur": 2, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150114, "dur": 276, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150391, "dur": 1, "ph": "X", "name": "ProcessMessages 3348", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150393, "dur": 61, "ph": "X", "name": "ReadAsync 3348", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150456, "dur": 243, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150704, "dur": 5, "ph": "X", "name": "ProcessMessages 2400", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150711, "dur": 52, "ph": "X", "name": "ReadAsync 2400", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150764, "dur": 1, "ph": "X", "name": "ProcessMessages 1740", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142150767, "dur": 458, "ph": "X", "name": "ReadAsync 1740", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151226, "dur": 1, "ph": "X", "name": "ProcessMessages 1611", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151228, "dur": 47, "ph": "X", "name": "ReadAsync 1611", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151276, "dur": 3, "ph": "X", "name": "ProcessMessages 2109", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151592, "dur": 28, "ph": "X", "name": "ReadAsync 2109", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151621, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151625, "dur": 130, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151757, "dur": 1, "ph": "X", "name": "ProcessMessages 2543", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142151759, "dur": 269, "ph": "X", "name": "ReadAsync 2543", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152030, "dur": 3, "ph": "X", "name": "ProcessMessages 5292", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152033, "dur": 177, "ph": "X", "name": "ReadAsync 5292", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152212, "dur": 1, "ph": "X", "name": "ProcessMessages 1463", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152213, "dur": 46, "ph": "X", "name": "ReadAsync 1463", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152262, "dur": 258, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152534, "dur": 1, "ph": "X", "name": "ProcessMessages 2260", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152536, "dur": 238, "ph": "X", "name": "ReadAsync 2260", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142152781, "dur": 479, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153263, "dur": 2, "ph": "X", "name": "ProcessMessages 3619", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153266, "dur": 56, "ph": "X", "name": "ReadAsync 3619", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153324, "dur": 396, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153722, "dur": 1, "ph": "X", "name": "ProcessMessages 1402", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153723, "dur": 69, "ph": "X", "name": "ReadAsync 1402", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142153795, "dur": 300, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154099, "dur": 4, "ph": "X", "name": "ProcessMessages 1306", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154105, "dur": 101, "ph": "X", "name": "ReadAsync 1306", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154209, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154212, "dur": 372, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154587, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142154590, "dur": 440, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155032, "dur": 2, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155036, "dur": 385, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155426, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155428, "dur": 237, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155668, "dur": 7, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155677, "dur": 195, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155874, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142155875, "dur": 309, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142156187, "dur": 133, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142156322, "dur": 181, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142156505, "dur": 123, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142156630, "dur": 447, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157080, "dur": 193, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157275, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157276, "dur": 247, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157526, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157528, "dur": 348, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157878, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142157879, "dur": 190, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142158072, "dur": 441, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142158515, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142158517, "dur": 368, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142158889, "dur": 301, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159191, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159193, "dur": 174, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159369, "dur": 121, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159494, "dur": 156, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159651, "dur": 1, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159654, "dur": 214, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142159870, "dur": 156, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160029, "dur": 115, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160147, "dur": 109, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160259, "dur": 74, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160342, "dur": 367, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160712, "dur": 166, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142160880, "dur": 197, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161079, "dur": 158, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161240, "dur": 562, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161808, "dur": 4, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161813, "dur": 43, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161858, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142161860, "dur": 272, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162134, "dur": 102, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162238, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162240, "dur": 55, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162304, "dur": 83, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162389, "dur": 64, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162455, "dur": 429, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142162887, "dur": 290, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163179, "dur": 1, "ph": "X", "name": "ProcessMessages 2051", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163181, "dur": 315, "ph": "X", "name": "ReadAsync 2051", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163499, "dur": 206, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163707, "dur": 156, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163866, "dur": 24, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163892, "dur": 29, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142163924, "dur": 173, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164099, "dur": 169, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164269, "dur": 113, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164385, "dur": 345, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164731, "dur": 2, "ph": "X", "name": "ProcessMessages 2198", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164734, "dur": 187, "ph": "X", "name": "ReadAsync 2198", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142164923, "dur": 248, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165173, "dur": 29, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165205, "dur": 212, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165420, "dur": 305, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165726, "dur": 1, "ph": "X", "name": "ProcessMessages 2198", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165728, "dur": 267, "ph": "X", "name": "ReadAsync 2198", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142165997, "dur": 259, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142166258, "dur": 193, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142166453, "dur": 143, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142166598, "dur": 166, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142166767, "dur": 245, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167016, "dur": 337, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167355, "dur": 90, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167446, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167448, "dur": 164, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167614, "dur": 301, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142167917, "dur": 208, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142168128, "dur": 81, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142168211, "dur": 278, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142168491, "dur": 152, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142168645, "dur": 199, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142168847, "dur": 217, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169066, "dur": 263, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169332, "dur": 173, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169506, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169508, "dur": 368, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169879, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142169881, "dur": 178, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170061, "dur": 162, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170225, "dur": 156, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170383, "dur": 220, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170606, "dur": 176, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170784, "dur": 66, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142170851, "dur": 174, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171027, "dur": 157, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171186, "dur": 16, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171212, "dur": 203, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171417, "dur": 197, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171617, "dur": 375, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142171994, "dur": 281, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142172277, "dur": 126, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142172405, "dur": 412, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142172819, "dur": 1, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142172822, "dur": 156, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142172980, "dur": 219, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142173201, "dur": 378, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142173582, "dur": 105, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142173688, "dur": 237, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142173928, "dur": 236, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142174167, "dur": 488, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142174657, "dur": 1, "ph": "X", "name": "ProcessMessages 1940", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142174659, "dur": 26, "ph": "X", "name": "ReadAsync 1940", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142174688, "dur": 409, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175099, "dur": 63, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175165, "dur": 51, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175219, "dur": 274, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175495, "dur": 196, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175694, "dur": 168, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142175864, "dur": 221, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176087, "dur": 15, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176105, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176124, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176144, "dur": 320, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176466, "dur": 16, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176485, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176522, "dur": 23, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176546, "dur": 189, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176737, "dur": 159, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176898, "dur": 17, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142176923, "dur": 171, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177103, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177105, "dur": 172, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177279, "dur": 184, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177464, "dur": 15, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177481, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177499, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177516, "dur": 35, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177554, "dur": 170, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177726, "dur": 268, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142177996, "dur": 332, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178329, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178421, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178423, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178451, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178523, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178593, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178687, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178783, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178785, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178807, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178842, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178920, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142178926, "dur": 124, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179051, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179056, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179095, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179187, "dur": 81, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179271, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179302, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179386, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179496, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179560, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179664, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179732, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179761, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142179930, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180037, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180122, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180197, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180331, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180365, "dur": 132, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180499, "dur": 133, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180634, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180685, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180720, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180910, "dur": 70, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180984, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142180986, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181043, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181169, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181172, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181218, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181220, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181292, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181294, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181390, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181430, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181512, "dur": 68, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181582, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181600, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181781, "dur": 82, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181865, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181886, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181961, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142181987, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182087, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182113, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182210, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182285, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182318, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182382, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182454, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182477, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182512, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182581, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182601, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182628, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182681, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182708, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182710, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182780, "dur": 199, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182980, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142182981, "dur": 81, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183065, "dur": 70, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183137, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183208, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183280, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183314, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183389, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183410, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183441, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183494, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183511, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183537, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183580, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183612, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183646, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183698, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183724, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183768, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183795, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183827, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183890, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183924, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142183988, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184009, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184023, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184104, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184180, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184224, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184293, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184319, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184349, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184390, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184425, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184473, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184511, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184613, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184638, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184704, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142184725, "dur": 6753, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142191482, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142191484, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142191564, "dur": 1221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142192787, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142192862, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142192970, "dur": 2155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142195128, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142195130, "dur": 15955, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142211090, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142211091, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142211158, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142211175, "dur": 2216, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142213393, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142213395, "dur": 3919, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142217319, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142217321, "dur": 273, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142217597, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142217915, "dur": 443, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142218361, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142218554, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142218558, "dur": 205, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142218766, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142218835, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219085, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219190, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219228, "dur": 202, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219433, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219469, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142219766, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142220013, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142220260, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142220359, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142220515, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142220672, "dur": 290, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221002, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221006, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221107, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221204, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221432, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221591, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221725, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142221852, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142222100, "dur": 152, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142222254, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142222442, "dur": 583, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142223029, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142223137, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142223453, "dur": 654, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142224110, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142224136, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142224506, "dur": 292, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142224800, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142225235, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142225500, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142225579, "dur": 412, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142225993, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226019, "dur": 381, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226403, "dur": 102, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226507, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226579, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226596, "dur": 270, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226869, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142226933, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142227070, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142227200, "dur": 510, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142227712, "dur": 225, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142227940, "dur": 212, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228159, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228163, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228344, "dur": 112, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228460, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228602, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142228738, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229012, "dur": 487, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229501, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229611, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229689, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229835, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229837, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229905, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142229908, "dur": 327, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230237, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230457, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230525, "dur": 292, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230819, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230947, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142230950, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142231020, "dur": 466, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142231503, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142231685, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142231710, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142231925, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232043, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232178, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232182, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232251, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232444, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232446, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232586, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232766, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142232768, "dur": 553, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233333, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233334, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233389, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233697, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233699, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142233849, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234037, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234058, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234121, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234336, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234503, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234574, "dur": 190, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234766, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142234863, "dur": 431, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142235296, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142235298, "dur": 112641, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142347944, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142347947, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142347995, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142348729, "dur": 33, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142348765, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142348784, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142348804, "dur": 22, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142348828, "dur": 1712, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142350542, "dur": 2117, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142352663, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142353072, "dur": 393, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142353468, "dur": 523, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142353996, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142354000, "dur": 531, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142354535, "dur": 1079, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142355617, "dur": 533, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142356152, "dur": 1276, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142357432, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142357435, "dur": 382, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142357819, "dur": 397, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142358225, "dur": 1621, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142359856, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142359859, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142360012, "dur": 646, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142360661, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142360709, "dur": 606, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142361317, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142361684, "dur": 1239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142362925, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142363044, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142363047, "dur": 603, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142363653, "dur": 669, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142364324, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142364460, "dur": 695, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142365158, "dur": 509, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142365669, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142365764, "dur": 742, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142366509, "dur": 791, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142367301, "dur": 350, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142367653, "dur": 1158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142368815, "dur": 639, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142369457, "dur": 1900, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142371361, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142371363, "dur": 347, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142371713, "dur": 741, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142372457, "dur": 1481, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142374770, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142374778, "dur": 535, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142375317, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142375319, "dur": 437, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142375759, "dur": 871, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142376633, "dur": 579, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377215, "dur": 233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377451, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377520, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377576, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377707, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377780, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377805, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142377906, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378003, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378100, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378175, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378392, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378550, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378682, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378759, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378791, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378927, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142378997, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379112, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379185, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379214, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379298, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379353, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379473, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379526, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379598, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379627, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379689, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379808, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379859, "dur": 121, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142379981, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380005, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380233, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380263, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380304, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380440, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380502, "dur": 186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380690, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380728, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380787, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380823, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380856, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142380919, "dur": 202, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142381123, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142381124, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142381155, "dur": 1169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142382326, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142382328, "dur": 710, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383045, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383050, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383105, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383107, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383203, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383375, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142383377, "dur": 297982, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142681366, "dur": 30, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142681397, "dur": 1038, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142682439, "dur": 1924, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142684365, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142684368, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893142684485, "dur": 340719, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025229, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025232, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025301, "dur": 28, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025346, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025404, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025446, "dur": 84, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025544, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025571, "dur": 63, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025636, "dur": 121, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025760, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025787, "dur": 8, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025796, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025821, "dur": 25, "ph": "X", "name": "ProcessMessages 5112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143025847, "dur": 8114, "ph": "X", "name": "ReadAsync 5112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143033966, "dur": 4, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143033971, "dur": 594, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143034567, "dur": 289, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143034857, "dur": 1268, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143036127, "dur": 1040, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748893143037170, "dur": 12277, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143080587, "dur": 7004, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893141703235, "dur": 415307, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893142118549, "dur": 13, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748893142118564, "dur": 23703, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143087594, "dur": 28, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1427, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893141592983, "dur": 1458188, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893141605343, "dur": 90941, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893143051294, "dur": 6594, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893143054777, "dur": 95, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748893143058077, "dur": 204, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143087624, "dur": 33, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748893141762817, "dur": 3759, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893141766590, "dur": 30274, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893141797167, "dur": 411, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748893141797579, "dur": 185, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893141797939, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893141798044, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893141798237, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893141798420, "dur": 236963, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142035535, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142035691, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142036055, "dur": 182, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142036458, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142036534, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142036648, "dur": 84609, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142121393, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142121526, "dur": 169, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142121924, "dur": 350, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142122971, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142123094, "dur": 19089, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142142316, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142142451, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142142685, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748893142143773, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748893142143949, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748893142144394, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142144816, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748893142148003, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748893142148226, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748893142149093, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748893142150130, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748893142152484, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748893142152552, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748893142154562, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748893142154890, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748893142156735, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748893142159208, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748893142159295, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748893142160446, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748893142160981, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748893141797771, "dur": 380918, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893142178697, "dur": 857072, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143035815, "dur": 191, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143036084, "dur": 672, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143036796, "dur": 54, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143036856, "dur": 150, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143037029, "dur": 2658, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748893141797694, "dur": 381042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142178743, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748893142178949, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142179380, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142179834, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142180345, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142180439, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142180531, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142180601, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142180696, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142180833, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142180909, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142181086, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142181259, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142181329, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748893142181642, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142181911, "dur": 10224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142192135, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142192381, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142192432, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142192515, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142193225, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142193984, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142194654, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142195336, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142196213, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142196862, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142197543, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142198299, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142199001, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142199701, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142200385, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142201066, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142201808, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142202588, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142203319, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142204023, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142204766, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142205470, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142206167, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142206986, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142208205, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142209029, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142209725, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142210395, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142211066, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142211763, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142212542, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142213214, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142213878, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142214637, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142215604, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142216273, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142216960, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142217242, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142217605, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142218103, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142218207, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142218279, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142218788, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142219183, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142219506, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142219560, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142220714, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142221066, "dur": 2750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142223816, "dur": 814, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142224668, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142225047, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142225206, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142225724, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142225802, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142226118, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142226271, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142227323, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142227769, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142228007, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142229849, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142230161, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142231049, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142231105, "dur": 1299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142232420, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142232737, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142233464, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142234203, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748893142234334, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142234828, "dur": 116141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142350969, "dur": 2907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142353907, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142356345, "dur": 4177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142360523, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142360639, "dur": 3198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142363837, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142363904, "dur": 3248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142367203, "dur": 3463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142370667, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142370747, "dur": 5511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142376259, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142376343, "dur": 4204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748893142380548, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142380791, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748893142380889, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142380945, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748893142381039, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142381210, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748893142381280, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142381355, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142381418, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnity.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748893142381496, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142381827, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142381920, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142382079, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142382196, "dur": 1915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893142384112, "dur": 650508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748893143034638, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748893143034633, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748893143034890, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893141797689, "dur": 381019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142178978, "dur": 664, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1748893142179642, "dur": 1858, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1748893142181511, "dur": 705, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1748893142178716, "dur": 3501, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142182370, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142182467, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142182524, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748893142182665, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142182776, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142182876, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893142183049, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183137, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183195, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893142183397, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183514, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183626, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183712, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183777, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142183954, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184017, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184149, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748893142184368, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184460, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184516, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184586, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184677, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184764, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184886, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142184974, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185124, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185193, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185246, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748893142185376, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185437, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185501, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142185590, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142186377, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142187181, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142187842, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142188504, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142189238, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142189954, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142191218, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142192354, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142193119, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142193791, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142193868, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142194590, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142195270, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142196144, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142196807, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142197480, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142198252, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142198971, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142199672, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142200355, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142201033, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142201778, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142202546, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142203276, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142203960, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142204705, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142205417, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142206120, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142206863, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142208141, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142208962, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142209660, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142210337, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142211011, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142211685, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142212448, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142213144, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142213793, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142214584, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142215540, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142216198, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142216896, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142217607, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142218066, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142218206, "dur": 1578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142219784, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142220188, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142220274, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142220902, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142222007, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142222357, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142222501, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142223020, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142223111, "dur": 3316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142226427, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142226555, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142226681, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142226785, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142227376, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142227557, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142227817, "dur": 1740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142229558, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142229767, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142229881, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142231389, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142231618, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142231800, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142232623, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142232921, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142234193, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748893142234283, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142234757, "dur": 116186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142350943, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142353414, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142353591, "dur": 3434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142357027, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142357139, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142360850, "dur": 3380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142364275, "dur": 2352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142366678, "dur": 2945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142369625, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142369699, "dur": 4757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142374456, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142374520, "dur": 3301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142377823, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748893142377912, "dur": 6238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748893142384195, "dur": 651266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893141797688, "dur": 381034, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142178731, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748893142179030, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142179223, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142179412, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142179920, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142180246, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142180367, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142180444, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142180539, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142180624, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142180717, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142180864, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142180981, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748893142181256, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142181398, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142181663, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142181941, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142182199, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142182644, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748893142182807, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142182957, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183053, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183143, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183205, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183258, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183340, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183401, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142183543, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183644, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183751, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142183822, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748893142183983, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184036, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184108, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184216, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184364, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184467, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184590, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184683, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184774, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184872, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142184981, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185128, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185197, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185253, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185314, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185401, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185455, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185545, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142185626, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142186389, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142187190, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142187849, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142188516, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142189246, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142189961, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142191220, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142192373, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142193132, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142193830, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142193902, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142194603, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142195302, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142196164, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142196829, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142197500, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142198261, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142198986, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142199688, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142200369, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142201053, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142201791, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142202574, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142203304, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142203987, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142204733, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142205448, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142206150, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142206987, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142208195, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142209010, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142209711, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142210376, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142211051, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142211743, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142212545, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142213220, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142213887, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142214683, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142215625, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142216285, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142216974, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142217254, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142217611, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142218096, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142218431, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142218847, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142219327, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142219489, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142219684, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142219742, "dur": 1435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142221177, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142221505, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142221627, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142221705, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142221927, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142221982, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142222143, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142222530, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142223609, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142224081, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142225029, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142225087, "dur": 3549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142228636, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142228785, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142229023, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142231189, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142231318, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142231408, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142232386, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142232517, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748893142232665, "dur": 1623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142234288, "dur": 116602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142350891, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142353630, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142353753, "dur": 2816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142356615, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142359560, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142359656, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142362033, "dur": 4076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142366160, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142369661, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142369796, "dur": 4554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142374351, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142374510, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142377393, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142377465, "dur": 6214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142383771, "dur": 299194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142683169, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748893142682967, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893142684994, "dur": 240, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893143025908, "dur": 435, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748893142685549, "dur": 340838, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748893143034630, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748893143034604, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748893143034895, "dur": 548, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748893141797724, "dur": 381037, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142178769, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142179453, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142179562, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142179827, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142180327, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142180418, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142180510, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142180632, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142180781, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142180894, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142181000, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748893142181358, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142181485, "dur": 12123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142193608, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142193792, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142196036, "dur": 15822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142211859, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142211995, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142212113, "dur": 2159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142214309, "dur": 3428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142217738, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142218000, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142218114, "dur": 1668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142219783, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142220006, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142220144, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142220404, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142220466, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142221184, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142221314, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142221836, "dur": 4187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142226024, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142226393, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142227885, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142228201, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142228325, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142228441, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142228524, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142229395, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142229608, "dur": 1769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142231377, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142231460, "dur": 2811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142234272, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142235547, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748893142235637, "dur": 115272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142350909, "dur": 2616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142353525, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142353587, "dur": 4747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142358391, "dur": 2565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142360957, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142361027, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142364811, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142364869, "dur": 3709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142368579, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142368661, "dur": 3944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142372605, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142372698, "dur": 3950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142376649, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142376759, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748893142379547, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142379711, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142379853, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142379987, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380060, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380145, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380352, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380429, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380671, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748893142380754, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142380830, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748893142380906, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142381114, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142381245, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748893142381313, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142381467, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748893142381553, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142381756, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142381870, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142382213, "dur": 2015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748893142384228, "dur": 651244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893141797722, "dur": 381028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142178757, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142179334, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142179403, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142179588, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142179726, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142179915, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142180252, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142180387, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142180443, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142180557, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142180628, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142180780, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142180860, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142181026, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142181139, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142181434, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142181622, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142181881, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142182163, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748893142182421, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142182486, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142182657, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142182768, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142182826, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142182903, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142183059, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183147, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142183290, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183349, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183457, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183572, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183662, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183736, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142183944, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184006, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184071, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142184229, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184284, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184380, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184477, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184554, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184629, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184712, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184798, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142184888, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748893142184969, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185120, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185192, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185284, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185367, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185427, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185552, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142185681, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142186424, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142187215, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142187885, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142188575, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142189284, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142190004, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142191317, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142192436, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142193171, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142193894, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142194598, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142195280, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142196156, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142196820, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142197494, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142198257, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142198964, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142199648, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142200317, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142201008, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142201734, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142202529, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142203263, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142203948, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142204694, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142205411, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142206103, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142206826, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142208157, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142208982, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142209689, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142210352, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142211030, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142211708, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142212479, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142213174, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142213853, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142214605, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142215551, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142216204, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142216906, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142217278, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142217457, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142217612, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142218004, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142218154, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142218216, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142219692, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142220389, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142220673, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142222296, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142222671, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748893142222767, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142222862, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142223592, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142224170, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748893142225533, "dur": 527, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142348793, "dur": 989, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142226532, "dur": 123262, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748893142350890, "dur": 3918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142354810, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142354868, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142356906, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142357002, "dur": 4560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142361563, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142361644, "dur": 3582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142365227, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142365323, "dur": 2935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142368258, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142368317, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142371168, "dur": 6416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142377585, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748893142377666, "dur": 6526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748893142384230, "dur": 651293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893141797742, "dur": 381050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142178801, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142179229, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142179407, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142179558, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142179802, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142179932, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142180240, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142180332, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142180413, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142180486, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142180582, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142180679, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142180805, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142180950, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142181038, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893142181450, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893142181514, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748893142181571, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748893142181824, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893142182156, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748893142182338, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142182391, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142182456, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893142182669, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142182779, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142182857, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748893142183066, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183166, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183269, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183337, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183406, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183488, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183578, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142183681, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748893142184033, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184118, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184228, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184281, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184377, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184481, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184556, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184671, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184751, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184855, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142184954, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185056, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185237, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185305, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185375, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185438, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185507, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142185596, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142186668, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142187389, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142188041, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142188726, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142189427, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142190183, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142191715, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142192666, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142193323, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142194161, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142194867, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142195555, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142196418, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142197079, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142197759, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142198550, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142199238, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142199925, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142200606, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142201283, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142202083, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142202837, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142203529, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142204251, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142204979, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142205675, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142206374, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142207473, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142208494, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142209245, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142209941, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142210639, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142211307, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142212034, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142212104, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142212819, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142213496, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142214173, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142215107, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142215892, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142216579, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142217227, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142217396, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142217617, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142218076, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142218578, "dur": 1333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142219912, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142220169, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142220728, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142220826, "dur": 1223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142222049, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142222110, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142223060, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142223287, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142223805, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142224265, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142224421, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142225533, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142225684, "dur": 1671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142227355, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142227658, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142227847, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142229159, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142229320, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142230348, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142230559, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142230682, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142230852, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142231638, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748893142231904, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142232217, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142232283, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142232976, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142233088, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142233648, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142234292, "dur": 116650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142350942, "dur": 3351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142354341, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142356539, "dur": 4226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142360779, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142363757, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142367411, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142367492, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142370132, "dur": 3212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142373344, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142373467, "dur": 4695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748893142378163, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142379148, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142379272, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142379398, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748893142379538, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142379803, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380072, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380284, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380537, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380625, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380736, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142380955, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748893142381011, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142381119, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142381306, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748893142381376, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142381530, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142381613, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748893142381794, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142381860, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748893142381935, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142382186, "dur": 1574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142383761, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748893142384225, "dur": 651610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893141797771, "dur": 381040, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142178818, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142179303, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142179544, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142179909, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142180339, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142180508, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142180567, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142180709, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142180808, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142180949, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142181062, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142181221, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142181453, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748893142181728, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893142182076, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748893142182349, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142182460, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142182519, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893142182789, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142182853, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142182955, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893142183135, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183268, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183335, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183463, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183568, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183652, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183736, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183802, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142183940, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184000, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184069, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184134, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184277, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184330, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184420, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184492, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184561, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748893142184743, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184827, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142184933, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185029, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185106, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185168, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185231, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185353, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185444, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185516, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142185609, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142186419, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142187208, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142187853, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142188526, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142189260, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142189974, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142191522, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142192583, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142193247, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142194177, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142194862, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142195520, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142196378, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142197040, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142197739, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142198509, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142199189, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142199885, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142200564, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142201228, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142202016, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142202774, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142203478, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142204206, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142204927, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142205632, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142206330, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142207530, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142208562, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142209289, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142209986, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142210658, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142211325, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142212060, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142212142, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142212837, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142213509, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142214191, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142215114, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142215885, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142216570, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142217230, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142217374, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142217626, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142218003, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142218268, "dur": 5119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142223387, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142223879, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142223963, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142225247, "dur": 1729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142226976, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142227528, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142228114, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142228221, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142229081, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142229470, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142229572, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142229663, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142230257, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142231500, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142231892, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142232269, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142232334, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142232543, "dur": 1633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142234176, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142234544, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142234618, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142235296, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142235459, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748893142235523, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142235795, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142236950, "dur": 445252, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142682965, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142683352, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893142685115, "dur": 227, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893143025920, "dur": 829, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748893142685568, "dur": 341195, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748893143034631, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748893143034614, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748893143034881, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893141797771, "dur": 381055, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142178831, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142179318, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142179481, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142179535, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142179593, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142180295, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142180459, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142180563, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142180686, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142180808, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142180872, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142181031, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142181174, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142181337, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893142181527, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748893142181714, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142181797, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142181862, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893142182046, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748893142182197, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893142182424, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142182499, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893142182748, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142182802, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142182916, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748893142183057, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183197, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183257, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183349, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183436, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183522, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183627, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183716, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183777, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183934, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142183990, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184058, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184123, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184233, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184326, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184389, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184476, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184550, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184683, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184758, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184865, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142184997, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185092, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185149, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185215, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185272, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185338, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185423, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185479, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185573, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142185691, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142186410, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142187201, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142187859, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142188507, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142189242, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142189870, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142190911, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142192256, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142193038, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142193689, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142194438, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142195129, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142195976, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142196679, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142197337, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142198072, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142198823, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142199505, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142200200, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142200887, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142201598, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142202422, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142203146, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142203832, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142204565, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142205291, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142205989, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142206675, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142208028, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142208849, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142209546, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142210239, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142210907, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142211598, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142212374, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142213073, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142213728, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142214496, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142215452, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142216122, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142216817, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142217419, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142217611, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142218074, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142218488, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142219306, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142219440, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142219654, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142220834, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142221288, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142221364, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142221617, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142221897, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142222048, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142222702, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142222853, "dur": 2233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142225086, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142225424, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142226872, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142227448, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142227517, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142227616, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142227930, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142228845, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142228948, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142229214, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142232791, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142233068, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142233186, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142233269, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142233962, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142234191, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142234279, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142234842, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142235073, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142235185, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142235404, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142235531, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748893142235647, "dur": 115284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142350931, "dur": 4418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142355387, "dur": 3549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142358989, "dur": 3371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142362387, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142365437, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142367896, "dur": 4412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142372309, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142372410, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142374925, "dur": 8989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748893142383915, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893142384084, "dur": 650584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748893143034846, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748893143034903, "dur": 534, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748893143035438, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748893143044325, "dur": 2357, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1427, "tid": 4276, "ts": 1748893143093093, "dur": 15422, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1427, "tid": 4276, "ts": 1748893143108563, "dur": 2875, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1427, "tid": 4276, "ts": 1748893143070391, "dur": 42788, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}