{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1427, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1427, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1427, "tid": 4630, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1427, "tid": 4630, "ts": 1748894468121006, "dur": 485, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468131529, "dur": 662, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1427, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1427, "tid": 1, "ts": 1748894465414643, "dur": 5997, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748894465420643, "dur": 65376, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1427, "tid": 1, "ts": 1748894465486028, "dur": 55267, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468132195, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 1427, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465413287, "dur": 16789, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465430079, "dur": 2676212, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465430763, "dur": 4079, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465434846, "dur": 1110, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465435978, "dur": 13046, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465449028, "dur": 304, "ph": "X", "name": "ProcessMessages 2775", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465449637, "dur": 39, "ph": "X", "name": "ReadAsync 2775", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465449678, "dur": 4, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465449682, "dur": 39, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450036, "dur": 1, "ph": "X", "name": "ProcessMessages 1330", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450039, "dur": 42, "ph": "X", "name": "ReadAsync 1330", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450086, "dur": 3, "ph": "X", "name": "ProcessMessages 8182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450090, "dur": 22, "ph": "X", "name": "ReadAsync 8182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450114, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450116, "dur": 151, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450269, "dur": 41, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450312, "dur": 2, "ph": "X", "name": "ProcessMessages 4710", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450315, "dur": 59, "ph": "X", "name": "ReadAsync 4710", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450375, "dur": 1, "ph": "X", "name": "ProcessMessages 1911", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450377, "dur": 29, "ph": "X", "name": "ReadAsync 1911", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450408, "dur": 21, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450433, "dur": 46, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450483, "dur": 33, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450518, "dur": 1, "ph": "X", "name": "ProcessMessages 1440", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450520, "dur": 20, "ph": "X", "name": "ReadAsync 1440", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450542, "dur": 58, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450606, "dur": 1, "ph": "X", "name": "ProcessMessages 1174", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450608, "dur": 33, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450643, "dur": 58, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450709, "dur": 1, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450711, "dur": 39, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450751, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450757, "dur": 31, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450790, "dur": 106, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465450918, "dur": 143, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451070, "dur": 714, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451820, "dur": 2, "ph": "X", "name": "ProcessMessages 3439", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451824, "dur": 23, "ph": "X", "name": "ReadAsync 3439", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451851, "dur": 31, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451887, "dur": 34, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451925, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465451929, "dur": 181, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452119, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452120, "dur": 92, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452215, "dur": 1, "ph": "X", "name": "ProcessMessages 1675", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452217, "dur": 431, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452657, "dur": 44, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452702, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452704, "dur": 18, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452729, "dur": 26, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452757, "dur": 18, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452777, "dur": 28, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452812, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452861, "dur": 61, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452926, "dur": 10, "ph": "X", "name": "ProcessMessages 2465", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465452937, "dur": 98, "ph": "X", "name": "ReadAsync 2465", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453036, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453038, "dur": 34, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453074, "dur": 46, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453124, "dur": 22, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453149, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453172, "dur": 66, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453240, "dur": 1, "ph": "X", "name": "ProcessMessages 1703", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453242, "dur": 64, "ph": "X", "name": "ReadAsync 1703", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453313, "dur": 166, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453480, "dur": 1, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453482, "dur": 28, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453511, "dur": 1, "ph": "X", "name": "ProcessMessages 1324", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453554, "dur": 32, "ph": "X", "name": "ReadAsync 1324", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453587, "dur": 1, "ph": "X", "name": "ProcessMessages 1485", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453594, "dur": 26, "ph": "X", "name": "ReadAsync 1485", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453623, "dur": 70, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453694, "dur": 1, "ph": "X", "name": "ProcessMessages 1479", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453696, "dur": 31, "ph": "X", "name": "ReadAsync 1479", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453729, "dur": 25, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453755, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453757, "dur": 23, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453786, "dur": 17, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453810, "dur": 51, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453882, "dur": 1, "ph": "X", "name": "ProcessMessages 1454", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453884, "dur": 19, "ph": "X", "name": "ReadAsync 1454", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465453936, "dur": 120, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454058, "dur": 16, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454076, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454096, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454205, "dur": 21, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454231, "dur": 1, "ph": "X", "name": "ProcessMessages 2516", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454234, "dur": 39, "ph": "X", "name": "ReadAsync 2516", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454283, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454285, "dur": 55, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454341, "dur": 1, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454342, "dur": 26, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454369, "dur": 1, "ph": "X", "name": "ProcessMessages 1675", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454371, "dur": 19, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454392, "dur": 112, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454505, "dur": 2, "ph": "X", "name": "ProcessMessages 3344", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454507, "dur": 20, "ph": "X", "name": "ReadAsync 3344", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454553, "dur": 32, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454592, "dur": 18, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465454612, "dur": 9534, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465464475, "dur": 2, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465464478, "dur": 654, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465133, "dur": 3, "ph": "X", "name": "ProcessMessages 8116", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465138, "dur": 42, "ph": "X", "name": "ReadAsync 8116", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465208, "dur": 3, "ph": "X", "name": "ProcessMessages 8158", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465213, "dur": 23, "ph": "X", "name": "ReadAsync 8158", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465244, "dur": 6, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465465251, "dur": 36, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467670, "dur": 10, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467680, "dur": 35, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467719, "dur": 4, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467749, "dur": 25, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467775, "dur": 1, "ph": "X", "name": "ProcessMessages 1669", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467776, "dur": 19, "ph": "X", "name": "ReadAsync 1669", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467797, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467845, "dur": 61, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467912, "dur": 1, "ph": "X", "name": "ProcessMessages 1547", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467913, "dur": 40, "ph": "X", "name": "ReadAsync 1547", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467954, "dur": 1, "ph": "X", "name": "ProcessMessages 1482", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467956, "dur": 21, "ph": "X", "name": "ReadAsync 1482", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465467979, "dur": 46, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468029, "dur": 3, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468033, "dur": 39, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468079, "dur": 34, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468115, "dur": 1, "ph": "X", "name": "ProcessMessages 1629", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468116, "dur": 30, "ph": "X", "name": "ReadAsync 1629", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468150, "dur": 32, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468187, "dur": 18, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468205, "dur": 49, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468291, "dur": 46, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468341, "dur": 1, "ph": "X", "name": "ProcessMessages 1482", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468706, "dur": 34, "ph": "X", "name": "ReadAsync 1482", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468742, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468750, "dur": 36, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468788, "dur": 34, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468825, "dur": 51, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468882, "dur": 1, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468884, "dur": 17, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468904, "dur": 70, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465468976, "dur": 1234, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465470211, "dur": 3, "ph": "X", "name": "ProcessMessages 8182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465470219, "dur": 28, "ph": "X", "name": "ReadAsync 8182", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465470248, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465470250, "dur": 59, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465475085, "dur": 35, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465475122, "dur": 4, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465475127, "dur": 247, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465475377, "dur": 731, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465476119, "dur": 1, "ph": "X", "name": "ProcessMessages 2468", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465476121, "dur": 122, "ph": "X", "name": "ReadAsync 2468", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465476252, "dur": 312, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465476570, "dur": 1514, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465478085, "dur": 3, "ph": "X", "name": "ProcessMessages 3657", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465478088, "dur": 517, "ph": "X", "name": "ReadAsync 3657", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465478609, "dur": 911, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465479521, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465479523, "dur": 18, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465479543, "dur": 174, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465479719, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465479736, "dur": 564, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465480303, "dur": 672, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465480978, "dur": 190, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481170, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481171, "dur": 456, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481635, "dur": 28, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481665, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481666, "dur": 45, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465481713, "dur": 731, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465482447, "dur": 18, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465482467, "dur": 237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465482706, "dur": 338, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465483045, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465483047, "dur": 564, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465483614, "dur": 80, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465483696, "dur": 620, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465484318, "dur": 36, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465484355, "dur": 1, "ph": "X", "name": "ProcessMessages 1866", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465484357, "dur": 911, "ph": "X", "name": "ReadAsync 1866", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465485271, "dur": 197, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465485471, "dur": 812, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465486284, "dur": 2, "ph": "X", "name": "ProcessMessages 2645", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465486289, "dur": 37, "ph": "X", "name": "ReadAsync 2645", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465486329, "dur": 529, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465486861, "dur": 537, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465487401, "dur": 552, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465487962, "dur": 484, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465488449, "dur": 30, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465488482, "dur": 576, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489063, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489065, "dur": 563, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489630, "dur": 73, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489706, "dur": 136, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489844, "dur": 125, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465489971, "dur": 441, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465490414, "dur": 151, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465490568, "dur": 687, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465491258, "dur": 850, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465492111, "dur": 478, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465492592, "dur": 892, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465493495, "dur": 1, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465493497, "dur": 505, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465494005, "dur": 31, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465494039, "dur": 521, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465494563, "dur": 94, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465494659, "dur": 553, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465495214, "dur": 77, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465495294, "dur": 452, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465495748, "dur": 20, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465495770, "dur": 525, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465496303, "dur": 585, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465496890, "dur": 106, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465496998, "dur": 471, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465497472, "dur": 263, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465497736, "dur": 1, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465497737, "dur": 26, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465497766, "dur": 55, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465497823, "dur": 662, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465498487, "dur": 259, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465498748, "dur": 186, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465498936, "dur": 123, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499061, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499094, "dur": 302, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499398, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499399, "dur": 171, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499572, "dur": 1, "ph": "X", "name": "ProcessMessages 2101", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499574, "dur": 31, "ph": "X", "name": "ReadAsync 2101", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499607, "dur": 84, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499694, "dur": 44, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499740, "dur": 58, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499804, "dur": 5, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465499810, "dur": 210, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500025, "dur": 153, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500180, "dur": 473, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500655, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500656, "dur": 91, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500749, "dur": 1, "ph": "X", "name": "ProcessMessages 1474", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465500751, "dur": 623, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465501375, "dur": 1, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465501377, "dur": 56, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465501436, "dur": 608, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465502046, "dur": 686, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465502733, "dur": 1, "ph": "X", "name": "ProcessMessages 1862", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465502735, "dur": 322, "ph": "X", "name": "ReadAsync 1862", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465503059, "dur": 1, "ph": "X", "name": "ProcessMessages 2460", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465503061, "dur": 661, "ph": "X", "name": "ReadAsync 2460", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465503736, "dur": 17, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465503754, "dur": 685, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465504441, "dur": 572, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505014, "dur": 86, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505102, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505104, "dur": 92, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505199, "dur": 130, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505357, "dur": 102, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505465, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505530, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505618, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505675, "dur": 246, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505938, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465505944, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506037, "dur": 289, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506329, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506332, "dur": 84, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506423, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506426, "dur": 129, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506584, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506685, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506714, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506815, "dur": 17, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506846, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506955, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465506958, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507166, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507286, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507289, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507356, "dur": 244, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507603, "dur": 21, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507625, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507659, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465507820, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508022, "dur": 124, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508147, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508700, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508740, "dur": 128, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508871, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465508872, "dur": 331, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509205, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509218, "dur": 123, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509343, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509346, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465509461, "dur": 1153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465510616, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465510618, "dur": 567, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465511211, "dur": 250, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465511464, "dur": 377, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465511844, "dur": 446, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465512302, "dur": 629, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465512934, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465513200, "dur": 627, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465513829, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465513830, "dur": 430, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514282, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514286, "dur": 24, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514312, "dur": 289, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514602, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514604, "dur": 146, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514751, "dur": 122, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514876, "dur": 121, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465514999, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515054, "dur": 394, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515449, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515451, "dur": 94, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515547, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515644, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515735, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515812, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515886, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465515930, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516031, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516145, "dur": 57, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516204, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516245, "dur": 154, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516409, "dur": 120, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516531, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465516617, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517087, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517094, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517352, "dur": 7, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517361, "dur": 131, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517493, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465517495, "dur": 538, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518037, "dur": 12, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518051, "dur": 44, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518097, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518147, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518325, "dur": 182, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518509, "dur": 152, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518664, "dur": 205, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518871, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465518983, "dur": 49, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465519034, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465519093, "dur": 299, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465519396, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465519400, "dur": 7037, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465526444, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465526447, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465526512, "dur": 10703, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465537219, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465537223, "dur": 14448, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465551675, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465551678, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465551734, "dur": 2030, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465553767, "dur": 5793, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465559563, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465559685, "dur": 529, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465560221, "dur": 24, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465560247, "dur": 897, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561146, "dur": 37, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561185, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561207, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561288, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561655, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561743, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465561948, "dur": 254, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465562205, "dur": 362, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465562568, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465562569, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465562726, "dur": 415, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465563156, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465563305, "dur": 367, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465563674, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465563675, "dur": 421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465564098, "dur": 456, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465564559, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465564822, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465564823, "dur": 910, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465565742, "dur": 707, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465566451, "dur": 404, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465566857, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465566933, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465567033, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465567056, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465567130, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465567189, "dur": 753, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465567945, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465568016, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465568021, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465568232, "dur": 601, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465568835, "dur": 267, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569103, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569188, "dur": 328, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569517, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569802, "dur": 13, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569816, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465569970, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465570218, "dur": 566, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465570786, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465571033, "dur": 471, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465571507, "dur": 776, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465572286, "dur": 352, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465572641, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465572893, "dur": 1236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465574132, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465574408, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465574676, "dur": 338, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465575016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465575020, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465575229, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465575423, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465575689, "dur": 1078, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465576774, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465576779, "dur": 326, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465577108, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465577306, "dur": 253, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465577561, "dur": 477, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465578042, "dur": 540, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465578584, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465578657, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465578810, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465579218, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465579222, "dur": 334, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465579583, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465579587, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465579824, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580061, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580265, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580269, "dur": 107, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580379, "dur": 176, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580558, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465580561, "dur": 607, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465581198, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465581201, "dur": 305, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465581510, "dur": 377, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465581891, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465581894, "dur": 504, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465582401, "dur": 137, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465582540, "dur": 370, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465582912, "dur": 392, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465583307, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465583472, "dur": 334, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465583808, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584018, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584141, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584145, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584324, "dur": 282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584608, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584610, "dur": 230, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584843, "dur": 143, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465584988, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465585272, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465585323, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465585692, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465585909, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465586083, "dur": 778, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465586863, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465586866, "dur": 233, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465587101, "dur": 209, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465587311, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465587313, "dur": 591, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465587906, "dur": 179, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588088, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588192, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588394, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588399, "dur": 192, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588593, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465588857, "dur": 384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465589244, "dur": 751, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465589997, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590061, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590065, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590229, "dur": 347, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590578, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590580, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590949, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465590952, "dur": 92480, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465683440, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465683442, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465683490, "dur": 102, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465683934, "dur": 37, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465683974, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465684015, "dur": 30, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465684048, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465684067, "dur": 2290, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465686359, "dur": 1300, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465687663, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465688021, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465688166, "dur": 755, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465688924, "dur": 891, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465689822, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465689861, "dur": 990, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465690853, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465691184, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465691571, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465691575, "dur": 711, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465692289, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465692291, "dur": 998, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465693292, "dur": 256, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465693551, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465693554, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465693826, "dur": 518, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465694346, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465694457, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465694490, "dur": 781, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465695277, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465695280, "dur": 1094, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465696377, "dur": 421, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465696800, "dur": 939, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465697741, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465697910, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465698010, "dur": 288, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465698304, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465698308, "dur": 1136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465699447, "dur": 653, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465700102, "dur": 442, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465700547, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465700560, "dur": 573, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701139, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701281, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701392, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701394, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701654, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465701856, "dur": 1546, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465703404, "dur": 418, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465703825, "dur": 416, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465704242, "dur": 408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465704653, "dur": 570, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465705225, "dur": 751, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465705982, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465705985, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465706222, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465706469, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465706669, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465706671, "dur": 405, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707079, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707081, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707232, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707331, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707375, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707456, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707576, "dur": 12, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707589, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707617, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707647, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707702, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707761, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707799, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707859, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707878, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707902, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465707962, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708015, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708041, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708070, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708178, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708274, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708354, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708373, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708407, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708441, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708509, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708559, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708565, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708595, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708666, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708687, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708746, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708802, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708824, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708850, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708920, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465708993, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709082, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709113, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709135, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709154, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709202, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709227, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709262, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709302, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709385, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709415, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709442, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709501, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709547, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709604, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709688, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709710, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709744, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709828, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709902, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709979, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465709999, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710024, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710054, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710126, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710156, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710193, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710215, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710238, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465710450, "dur": 605, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711059, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711113, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711225, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711282, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711374, "dur": 201, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711582, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711689, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894465711777, "dur": 2070411, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467782223, "dur": 557, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467782785, "dur": 7288, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467790086, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467790095, "dur": 4544, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467794656, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467794662, "dur": 202591, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467997259, "dur": 35, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894467997296, "dur": 3032, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468000332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468000334, "dur": 56299, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468056641, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468056643, "dur": 84, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468056730, "dur": 622, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468057356, "dur": 372, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468057732, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468057787, "dur": 23, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468057811, "dur": 2931, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468060744, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468060747, "dur": 726, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468061475, "dur": 37, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468061512, "dur": 36616, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098135, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098137, "dur": 89, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098228, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098290, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098380, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098433, "dur": 19, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468098453, "dur": 3052, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468101507, "dur": 21, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468101528, "dur": 212, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468101742, "dur": 270, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 1427, "tid": 12884901888, "ts": 1748894468102013, "dur": 4221, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468132219, "dur": 13578, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1427, "tid": 8589934592, "ts": 1748894465410137, "dur": 131264, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748894465541418, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1427, "tid": 8589934592, "ts": 1748894465541427, "dur": 2788, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468145808, "dur": 33, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1427, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1427, "tid": 4294967296, "ts": 1748894465273910, "dur": 2834012, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748894465279268, "dur": 119422, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748894468109272, "dur": 7137, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748894468114541, "dur": 54, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1427, "tid": 4294967296, "ts": 1748894468116493, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468145844, "dur": 315, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748894465427066, "dur": 3409, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894465430480, "dur": 19017, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894465449550, "dur": 77, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748894465449627, "dur": 111, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894465450231, "dur": 316, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748894465450904, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748894465455890, "dur": 9535, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748894465465812, "dur": 241, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748894465466477, "dur": 2099, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748894465469454, "dur": 153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748894465470282, "dur": 792, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748894465472183, "dur": 3810, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748894465449745, "dur": 54709, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894465504463, "dur": 2597819, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894468102514, "dur": 1052, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748894465449682, "dur": 54790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465504477, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748894465504670, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465505189, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465505407, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465505620, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465505758, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465505881, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465506022, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465506138, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465506293, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465506465, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465506639, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465506767, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465507043, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465507221, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465507481, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465507615, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465507790, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465507959, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465508153, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465508338, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465508568, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465508742, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465509000, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465509210, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465509450, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465509516, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465509772, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465509848, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465510051, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465510161, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465510477, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465513428, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465513758, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465513886, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748894465514248, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465514555, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465514647, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465514858, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465514934, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465514989, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515262, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515377, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465515434, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515511, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465515578, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515663, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515781, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465515907, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516032, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516189, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748894465516494, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516626, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516690, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516760, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516873, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465516953, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517012, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517130, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465517380, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517538, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517647, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517745, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517801, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517865, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465517947, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518043, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518112, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518264, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518384, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465518456, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518594, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748894465518665, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518767, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465518957, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519175, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519328, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519456, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519616, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519796, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465519979, "dur": 2980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465524106, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748894465522960, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465525367, "dur": 2360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465527728, "dur": 2730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465530795, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Mvc.Formatters.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748894465531777, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Mvc.ApiExplorer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748894465530458, "dur": 3290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465533749, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465534529, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465535332, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465536078, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465537056, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465538681, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465540066, "dur": 1757, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Math/Advanced/ReciprocalNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748894465540066, "dur": 3349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465543415, "dur": 2104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465545520, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465546321, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465547106, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465547902, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465548809, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465549646, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465550422, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465551127, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465551773, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465552503, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465552559, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465553261, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465553909, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465554573, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465555227, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465555953, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465556636, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465557265, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465557934, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465558599, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465559283, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465559955, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465560698, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465560968, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465561718, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465562009, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465562102, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465562719, "dur": 3046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465565765, "dur": 700, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465566468, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465567285, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465570183, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465570345, "dur": 1821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465572166, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465572221, "dur": 3175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465575396, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465575828, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_A1CE29DB57A110D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465575925, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465575983, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465576236, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465578122, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465578534, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465578826, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465578896, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465579351, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465579593, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465579785, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465579848, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465580034, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465580165, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465582332, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465582445, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465582606, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465583758, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465583955, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465584089, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465584587, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465584704, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465584843, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465585000, "dur": 1756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465586757, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748894465586834, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465587125, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465587287, "dur": 1556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465588889, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465589617, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465590746, "dur": 95246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465685992, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465688851, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465688975, "dur": 4077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465693053, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465693124, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465695436, "dur": 3340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465698777, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465698857, "dur": 3433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465702290, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465702346, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465704217, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465704282, "dur": 2705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465707031, "dur": 4787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748894465711880, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465712050, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465712106, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894465712340, "dur": 2389526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748894468101913, "dur": 301, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748894465449691, "dur": 54789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465504485, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748894465504646, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465505130, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465505433, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465505500, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465505585, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465505699, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465505940, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465506074, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465506137, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465506317, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465506435, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465506698, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465506942, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465507122, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465507493, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465507673, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465507900, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465508078, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465508315, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465508456, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465508656, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465508818, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465509117, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465509405, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748894465509639, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465509820, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748894465509952, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465510505, "dur": 19719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465530309, "dur": 1263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465531587, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465531724, "dur": 3770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465535535, "dur": 16632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465552168, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465552442, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465552505, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465554593, "dur": 5578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465560171, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465560481, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465560935, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465563254, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465563510, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465564848, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465564906, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465567765, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465567904, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465567958, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465568020, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465568750, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465569613, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465570663, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465571497, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465571559, "dur": 3631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465575190, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465575365, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465575485, "dur": 6231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465581717, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465581927, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465583011, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465585502, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465585605, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465585927, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465586405, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465587133, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748894465587702, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465588670, "dur": 2123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465590793, "dur": 95230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465686024, "dur": 4261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465690286, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465690360, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465692973, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465695322, "dur": 1869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465697239, "dur": 2997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465700271, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465702588, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465705077, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465705143, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465707334, "dur": 4670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748894465712057, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465712138, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748894465712536, "dur": 2389720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465449702, "dur": 54787, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465504493, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465505043, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465505379, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465505459, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465505531, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465505645, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465505742, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465505874, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465506007, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465506185, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465506350, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465506486, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465506659, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465506810, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465507036, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465507199, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465507494, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465507660, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465507788, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465507918, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465508147, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465508349, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465508573, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465508689, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465508964, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465509145, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465509362, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465509524, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465509748, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465509837, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465510129, "dur": 3637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748894465513768, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748894465513900, "dur": 11644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465525545, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465526211, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465526435, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465526673, "dur": 2818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465530639, "dur": 842, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.FileSystemGlobbing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748894465531752, "dur": 919, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748894465529492, "dur": 3713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465533288, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465533393, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465534364, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465535092, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465535877, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465536743, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465537857, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465539754, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465540442, "dur": 1379, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/Geometry/BitangentVectorNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748894465540442, "dur": 2917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465543359, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465545479, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465546367, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465547178, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465547945, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465548832, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465549670, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465550440, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465551149, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465551815, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465552551, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465553250, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465553889, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465554525, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465555184, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465555885, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465556584, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465557195, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465557861, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465558508, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465559179, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465559861, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465560590, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465560977, "dur": 4474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465565451, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465565938, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465566005, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465566357, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465566412, "dur": 3217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465569629, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465569908, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465570017, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748894465571065, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465573585, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465573715, "dur": 4395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748894465578111, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465578297, "dur": 288, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465684135, "dur": 780, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465578789, "dur": 106136, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748894465685973, "dur": 3735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465689709, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465689787, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465692281, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465692364, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465695149, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465695207, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465697637, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465697694, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465700278, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465702077, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465702144, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465704640, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748894465704693, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465707908, "dur": 4697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748894465712620, "dur": 2389676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465449711, "dur": 54784, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465504499, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465504998, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465505481, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465505575, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465505694, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465505794, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465505976, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465506089, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465506164, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465506302, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465506427, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465506597, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465506774, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465507079, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465507254, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465507505, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465507655, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465507815, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465507989, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465508194, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465508388, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465508583, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465508721, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465508985, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465509229, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465509477, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465509655, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465509855, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465510072, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465510209, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465510470, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465510698, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465510926, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748894465512193, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465512347, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748894465513698, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748894465513781, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748894465514157, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465514302, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465514740, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465514871, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465514967, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515059, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515322, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515393, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465515582, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515698, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515800, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465515889, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465516009, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516077, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516153, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748894465516522, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516630, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516698, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516830, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516925, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465516994, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517082, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517227, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517354, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517504, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517598, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517714, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517782, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517856, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465517938, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518016, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518100, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518217, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518289, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465518479, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518567, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518710, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465518866, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465519055, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748894465519135, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465519303, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465519475, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465519635, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465519803, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465520011, "dur": 2980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465524058, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748894465522991, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465525427, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465527770, "dur": 2663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465530784, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748894465531754, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748894465530433, "dur": 3292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465533726, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465534521, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465535320, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465536073, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465537047, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465538586, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465540000, "dur": 1818, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Math/Trigonometry/HyperbolicTangentNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748894465540000, "dur": 3435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465543436, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465545550, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465546418, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465547241, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465548033, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465548930, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465549717, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465550497, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465551202, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465551876, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465552651, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465553320, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465553978, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465554654, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465555287, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465556007, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465556687, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465557336, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465558004, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465558658, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465559325, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465559997, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465560731, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465561362, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465562281, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465562562, "dur": 1523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465564165, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465565469, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465567511, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465567677, "dur": 6779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465574456, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465574975, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465577273, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465577397, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465577815, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465578028, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465578126, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465578197, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465578302, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465578402, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465578757, "dur": 4125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465582883, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465582959, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465583747, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465584902, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465585320, "dur": 1812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465587132, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465587762, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465588781, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465589004, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465589091, "dur": 1643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465590735, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748894465590864, "dur": 95152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465686023, "dur": 2935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465688959, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748894465689112, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465691806, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465694218, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465696708, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465698980, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465702053, "dur": 3427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465705518, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465707790, "dur": 4727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748894465712534, "dur": 2389735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465449717, "dur": 54822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465504544, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465505008, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465505182, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465505404, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465505597, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465505749, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465505845, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465506048, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465506150, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465506325, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465506460, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465506618, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465506732, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465507004, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465507140, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465507380, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465507537, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465507755, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465507857, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465508107, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465508336, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465508567, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465508673, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465508957, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465509109, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465509329, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465509527, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465509767, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465509842, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465510006, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465510174, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465510424, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465511483, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465512100, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465513089, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465513914, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465514635, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465514854, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465514930, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515027, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465515087, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515321, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515405, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515461, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515533, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465515732, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515837, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465515952, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465516460, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465516558, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465517048, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517171, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517330, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517492, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517595, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517666, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517778, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517852, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465517944, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518048, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518119, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518234, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518330, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518490, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518646, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748894465518753, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465518953, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519137, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519300, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519411, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519595, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519747, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465519929, "dur": 3035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465524066, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748894465522964, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465525395, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465530516, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748894465527858, "dur": 3196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465531744, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Authorization.Policy.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748894465531054, "dur": 2736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465533791, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465534549, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465535351, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465536116, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465537075, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465538683, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465540087, "dur": 1737, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/Texture/SampleTexture2DArrayNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748894465540087, "dur": 3323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465543411, "dur": 2114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465545526, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465546317, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465547099, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465547861, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465548778, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465549615, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465550390, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465551091, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465551756, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465552536, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465553252, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465553898, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465554557, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465555203, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465555928, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465556605, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465557244, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465557915, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465558568, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465559226, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465559912, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465560645, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465561140, "dur": 13736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465574877, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465575278, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465576458, "dur": 5139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465581597, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465582011, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465582088, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465582284, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465582465, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465582530, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465583591, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465584326, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465584478, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465584946, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465585038, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465586244, "dur": 878, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465587129, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465587193, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465587273, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465589658, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465589910, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465590026, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465590103, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465590572, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465590728, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748894465590947, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894465591205, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465591338, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894467781752, "dur": 54, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894465592516, "dur": 2189317, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894467785999, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748894467784723, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894467790232, "dur": 216, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894467791512, "dur": 206459, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748894467998708, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748894468000453, "dur": 194, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894468098800, "dur": 415, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748894468000790, "dur": 98432, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748894468101920, "dur": 296, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748894465449724, "dur": 54825, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465504556, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465505004, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465505413, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465505607, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465505721, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465505932, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465506049, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465506116, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465506253, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465506366, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465506526, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465506651, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465506832, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465507023, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465507168, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465507331, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465507521, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465507621, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465507854, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465508045, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465508291, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465508421, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465508612, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465508789, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465509076, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465509242, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465509459, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465509579, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465509723, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465509795, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465509923, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465510100, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465510692, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465510771, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465511555, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465513521, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465513846, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748894465514012, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748894465514242, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465514429, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748894465514591, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465514731, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465514876, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465514972, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515131, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515400, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515458, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515530, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515659, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515758, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515848, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465515962, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516016, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465516154, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516214, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516299, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516362, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516454, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465516714, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516866, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465516942, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517001, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517096, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517236, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517365, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517523, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517607, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517771, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517888, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465517951, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465518444, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465518625, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465518694, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465518771, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465518932, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465519104, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465519322, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465519432, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465519609, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465519777, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748894465519872, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465520048, "dur": 2921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465524056, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894465522969, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465525398, "dur": 2395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465527794, "dur": 2667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465530799, "dur": 718, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894465531749, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894465530462, "dur": 3294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465533756, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465534542, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465535338, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465536086, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465537011, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465538616, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465540001, "dur": 1827, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Math/Range/MaximumNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748894465540001, "dur": 3392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465543393, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465545748, "dur": 929, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1748894465546677, "dur": 1834, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1748894465548511, "dur": 638, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1748894465545460, "dur": 3690, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465549150, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465549950, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465550693, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465551391, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465552054, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465552800, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465553464, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465554134, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465554786, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465555425, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465556141, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465556809, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465557458, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465558140, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465558801, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465559482, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465560123, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465560863, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465561004, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465561119, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465563857, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465563949, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465565331, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465567511, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465567909, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465567965, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465569046, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465569676, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465570782, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465571715, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465573124, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465573335, "dur": 4503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465577839, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465578026, "dur": 2160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465580187, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465580506, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465582734, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465582827, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465582908, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465582968, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465583248, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465583403, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465583494, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465584084, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465584260, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465584607, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465585120, "dur": 2025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465587145, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465587268, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465587377, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465588004, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465588138, "dur": 2603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465590743, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748894465590952, "dur": 95021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465685974, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465688221, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465690883, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465690969, "dur": 4011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465695028, "dur": 3715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465698792, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465701248, "dur": 3238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465704497, "dur": 3384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894465707882, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465708279, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465708907, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465709762, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465711111, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465711926, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894465712064, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748894465712178, "dur": 2072547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894467785318, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894467784728, "dur": 8506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894467794663, "dur": 574, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894468057255, "dur": 1312, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748894467795929, "dur": 262646, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748894468061423, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894468061417, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894468061495, "dur": 774, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748894468062272, "dur": 40004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465449732, "dur": 54852, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465504590, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465505045, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465505465, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465505577, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465505631, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465505970, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465506081, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465506154, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465506231, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465506344, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465506481, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465506592, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465506872, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465507042, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465507263, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465507423, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465507590, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465507714, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465507904, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465508111, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465508308, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465508432, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465508617, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465508806, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465509107, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465509266, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748894465509474, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465510495, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748894465511565, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465513077, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465513866, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748894465514133, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465514262, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465514493, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465514566, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465514638, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465514792, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748894465514975, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515112, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515429, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515503, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515625, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515695, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515816, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465515947, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465516399, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465516558, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465516671, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465516806, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465516910, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465516974, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517055, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517166, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465517313, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517454, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465517584, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517664, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517776, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517850, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465517925, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465518041, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465518144, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465518276, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465518431, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465518567, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748894465518757, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465518921, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519100, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519274, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519397, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519514, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519690, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465519876, "dur": 3181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465524168, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748894465523058, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465525522, "dur": 2543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465530637, "dur": 833, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748894465528066, "dur": 3461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465531740, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.burst@7a907cf5a459/Unity.Burst.CodeGen/ILPostProcessingLegacy.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748894465531528, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465533687, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465534501, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465535306, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465536063, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465537004, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465538566, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465540320, "dur": 1052, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Math/Vector/TransformNode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748894465539988, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465542032, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465543778, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465545751, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465546473, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465547293, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465548102, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465548988, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465549779, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465550567, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465551272, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465551944, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465552699, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465553362, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465554017, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465554688, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465555316, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465556028, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465556726, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465557358, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465558030, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465558685, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465559372, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465560031, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465560759, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465561396, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465562708, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465562847, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465562902, "dur": 1490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465564408, "dur": 4090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465568498, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465568752, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465568847, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465569983, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465571036, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465573077, "dur": 4179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465577257, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465577418, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465578868, "dur": 748, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465579625, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465579785, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465579921, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465580045, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465580145, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465580658, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465580714, "dur": 1266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465581981, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465582212, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465582338, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465582724, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465584582, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465584746, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465584904, "dur": 1352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465586257, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465586753, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748894465586853, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465587775, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465588228, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465588283, "dur": 2504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465590788, "dur": 95224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465686018, "dur": 4571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465690589, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465690661, "dur": 3281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465693989, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465696312, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465698565, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465698619, "dur": 3053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465701701, "dur": 3307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465705009, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465705150, "dur": 1992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465707172, "dur": 4788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748894465711961, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465712060, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748894465712115, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465712171, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748894465712624, "dur": 2389637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465449739, "dur": 54853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465504595, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465505075, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465505191, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465505588, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465505850, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465505953, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465506128, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465506210, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465506384, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465506520, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465506668, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465506822, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465507012, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465507129, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465507313, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465507444, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465507600, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465507748, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465508001, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465508141, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465508385, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465508561, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465508730, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465508926, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465509139, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465509317, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465509461, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465509623, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465509852, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465510007, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465510139, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465510356, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465510788, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465510879, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465511427, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465512189, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465512294, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465513730, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465513902, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465514137, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465514267, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465514614, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465514786, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465514891, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748894465515039, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515202, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465515287, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515383, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515442, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515514, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515686, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515811, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515914, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465515987, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465516046, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465516111, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465516288, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465516351, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465516407, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465517248, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517438, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517565, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517659, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517763, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517828, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517901, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465517974, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518082, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518148, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518295, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518401, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465518552, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518697, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518780, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465518969, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465519123, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748894465519191, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465519352, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465519498, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465519682, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465519805, "dur": 3136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465524136, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.inputsystem@ea5ab5b33a26/Tests/TestFixture/ScopedDisposable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748894465522941, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465525372, "dur": 2386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465527758, "dur": 2677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465530783, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748894465531758, "dur": 933, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748894465530435, "dur": 3267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465533702, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465534513, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465535326, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465536095, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465537069, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465538740, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465540158, "dur": 1087, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/Matrix/Matrix4Node.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748894465540158, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465541294, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465542575, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465544476, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465546037, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465546792, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465547565, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465548393, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465549287, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465550093, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465550802, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465551488, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465552166, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465552933, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465553584, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465554275, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465554918, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465555614, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465556299, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465556963, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465557620, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465558277, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465558936, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465559621, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465560251, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465560998, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465561165, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465562394, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465562671, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465563383, "dur": 13774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465577157, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465577527, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465577902, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465580804, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465580980, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581056, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581227, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581561, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581735, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581824, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581915, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465581989, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465582252, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465585485, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465585773, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465586099, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465586152, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465586693, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465587105, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465587942, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465588778, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465589148, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748894465589261, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465589756, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465589869, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465589990, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465590802, "dur": 95225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465686028, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465688450, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465688522, "dur": 3480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465692003, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465692103, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465694499, "dur": 3524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465698063, "dur": 2657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465700721, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894465700790, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465703450, "dur": 3160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465706645, "dur": 5659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748894465712327, "dur": 2349091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748894468061425, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748894468061419, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748894468061495, "dur": 782, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748894468062279, "dur": 39971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748894468104936, "dur": 1865, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1427, "tid": 4630, "ts": 1748894468148610, "dur": 123492, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1427, "tid": 4630, "ts": 1748894468273509, "dur": 2394, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1427, "tid": 4630, "ts": 1748894468129696, "dur": 150420, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}